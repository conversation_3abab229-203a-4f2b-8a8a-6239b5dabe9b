# Excel to MySQL Importer - Usage Guide

## 🚀 Getting Started

### 1. Start the Application
```bash
# Backend
cd backend
python start_server.py

# Frontend (new terminal)
cd frontend
npm start
```

### 2. Access the Application
- Open http://localhost:3000 in your browser
- Backend API docs: http://localhost:8000/docs

## 📊 Import Options

### Option 1: Quick Import
**Best for**: Simple data imports without specific structure requirements

1. Upload Excel file
2. Preview data
3. Click "Quick Import"
4. Data is stored in generic format

### Option 2: Column Mapping
**Best for**: Importing to existing schemas (employees, customers)

1. Upload Excel file
2. Click "Import with Column Mapping"
3. Select target schema (employees/customers)
4. Review auto-mapping suggestions
5. Adjust mappings as needed
6. Import with validation

### Option 3: Create New Table ⭐ NEW
**Best for**: Creating custom database tables from your Excel structure

1. Upload Excel file
2. Click "Create New Table"
3. Enter table name and description
4. Review field preview and types
5. Click "Create Table & Import"

## 🎯 Column Mapping Features

### Auto-Mapping Intelligence
The system automatically suggests mappings based on column name similarity:

**Employee Schema Mappings:**
- "Full Name" → "name"
- "Email Address" → "email"
- "Age" / "Years" → "age"
- "Department" / "Dept" → "department"
- "Salary" / "Annual Salary" → "salary"
- "Hire Date" / "Start Date" → "hire_date"
- "Phone" / "Contact Number" → "phone"

**Customer Schema Mappings:**
- "Company" → "company_name"
- "Contact Person" → "contact_name"
- "Business Email" → "email"
- "Phone Number" → "phone"
- "Street Address" → "address"
- "City Name" → "city"
- "Country Code" → "country"

### Field Types
- **Text**: General text data
- **Number**: Numeric values (integers, decimals)
- **Email**: Email addresses with validation
- **Date**: Date values

## 🔧 Dynamic Table Creation

### How It Works
1. **Analyze Excel Structure**: Examines your columns and data types
2. **Generate Field Names**: Converts Excel column names to database-friendly names
3. **Detect Field Types**: Automatically determines appropriate data types
4. **Create Physical Table**: Creates actual MySQL table
5. **Import Data**: Imports your Excel data to the new table

### Field Name Conversion
- Spaces → underscores: "Full Name" → "full_name"
- Special characters removed: "Email@Address" → "email_address"
- Lowercase conversion: "DEPARTMENT" → "department"
- Invalid names fixed: "123abc" → "field_1"

### Type Detection Rules
- **Number**: Numeric columns (integers, decimals)
- **Email**: Text containing "@" and "."
- **Date**: Date/datetime columns
- **Text**: Everything else (default)

## 📁 Sample Files

Use the provided sample files to test different scenarios:

### sample_employees.xlsx
- **Purpose**: Test employee schema mapping
- **Columns**: Full Name, Email Address, Years, Dept, Annual Salary, Start Date, Contact Number
- **Best for**: Testing auto-mapping accuracy

### sample_customers.xlsx
- **Purpose**: Test customer schema mapping
- **Columns**: Company, Contact Person, Business Email, Phone Number, Street Address, City Name, Country Code
- **Best for**: Testing different schema types

### sample_mixed_format.xlsx
- **Purpose**: Test underscore naming conventions
- **Columns**: employee_name, work_email, age_years, department_name, yearly_compensation, hire_date, mobile_phone
- **Best for**: Testing naming convention handling

### sample_large_dataset.xlsx
- **Purpose**: Performance testing
- **Records**: 100 employee records
- **Best for**: Testing import speed and batch processing

### sample_problematic_data.xlsx
- **Purpose**: Data validation testing
- **Issues**: Empty values, invalid emails, bad data types
- **Best for**: Testing error handling and validation

## 🎨 User Interface Guide

### Main Interface
- **File Upload**: Drag & drop or click to select Excel files
- **Preview**: Shows first 5 rows of your data
- **Import Options**: Three buttons for different import methods

### Column Mapping Interface
- **Schema Selector**: Choose target schema
- **Mapping Table**: Visual column-to-field mapping
- **Auto-suggestions**: Smart mapping recommendations
- **Validation**: Required field indicators
- **Statistics**: Shows mapping progress

### Create Table Interface
- **Table Configuration**: Name and description input
- **Field Preview**: Shows how Excel columns will become database fields
- **Type Detection**: Automatic field type assignment
- **Validation**: Table name validation

## 🔍 Troubleshooting

### Common Issues

**"Data too long for column" Error**
- Solution: Use "Create New Table" option for better field sizing

**"Access denied" Database Error**
- Check MySQL credentials in `backend/config.py`
- Ensure MySQL server is running

**Import Timeout**
- Large files may timeout
- Try smaller files or increase timeout settings

**Column Mapping Not Working**
- Ensure you've mapped all required fields
- Check that field types match your data

### Performance Tips
- Files under 10MB import fastest
- Use "Create New Table" for optimal field sizing
- Pre-clean data in Excel for best results

## 🎯 Best Practices

### Data Preparation
1. **Clean Headers**: Use clear, descriptive column names
2. **Consistent Data**: Ensure data types are consistent in each column
3. **Remove Empty Rows**: Delete completely empty rows
4. **Validate Emails**: Check email format before import

### Table Creation
1. **Descriptive Names**: Use clear table names (e.g., "customer_orders")
2. **Avoid Spaces**: Use underscores instead of spaces
3. **Meaningful Descriptions**: Add helpful descriptions for future reference

### Column Mapping
1. **Review Auto-suggestions**: Check suggested mappings before importing
2. **Map Required Fields**: Ensure all required fields are mapped
3. **Check Data Types**: Verify field types match your data

## 📈 Advanced Features

### Import History
- View all previous imports
- Track import sessions
- Monitor data volume

### Export Options
- Export imported data back to Excel
- Download by import session
- Maintain data integrity

### API Access
- Full REST API available at `/docs`
- Programmatic access to all features
- Integration with other systems

## 🎉 Success Indicators

### Successful Import
- ✅ Green success message
- ✅ Import statistics displayed
- ✅ Session ID provided
- ✅ Processing time shown

### Successful Table Creation
- ✅ "Table created successfully" message
- ✅ Field preview matches expectations
- ✅ Data imported to new table
- ✅ New schema available for future use

This comprehensive system handles everything from simple data imports to complex table creation, making Excel-to-database workflows seamless and efficient!