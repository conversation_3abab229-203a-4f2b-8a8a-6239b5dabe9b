.data-validator {
  background: var(--bg-tertiary);
  border-radius: var(--radius-large);
  overflow: hidden;
}

.validation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

.validation-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.validation-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--text-secondary);
}

.quality-score {
  font-size: 18px;
  font-weight: 700;
}

.validation-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1px;
  background: var(--border-light);
  margin: 16px;
  border-radius: var(--radius-medium);
  overflow: hidden;
}

.summary-item {
  background: var(--bg-tertiary);
  padding: 12px;
  text-align: center;
}

.summary-label {
  font-size: 11px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.summary-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.validation-issues {
  margin: 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--radius-medium);
  overflow: hidden;
}

.issues-header {
  padding: 12px 16px;
  background: #fee2e2;
  font-size: 13px;
  font-weight: 600;
  color: #991b1b;
  border-bottom: 1px solid #fecaca;
}

.issue-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid #fecaca;
}

.issue-item:last-child {
  border-bottom: none;
}

.issue-severity {
  width: 4px;
  height: 32px;
  border-radius: 2px;
}

.issue-content {
  flex: 1;
}

.issue-column {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.issue-message {
  font-size: 11px;
  color: var(--text-secondary);
}

.issues-more {
  padding: 8px 16px;
  font-size: 11px;
  color: var(--text-secondary);
  text-align: center;
  background: #fef7f7;
}

.validation-recommendations {
  margin: 16px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: var(--radius-medium);
  overflow: hidden;
}

.recommendations-header {
  padding: 12px 16px;
  background: #e0f2fe;
  font-size: 13px;
  font-weight: 600;
  color: #0c4a6e;
  border-bottom: 1px solid #bae6fd;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid #bae6fd;
}

.recommendation-item:last-child {
  border-bottom: none;
}

.recommendation-icon {
  font-size: 16px;
  margin-top: 2px;
}

.recommendation-content {
  flex: 1;
}

.recommendation-message {
  font-size: 12px;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.recommendation-action {
  font-size: 11px;
  color: var(--text-secondary);
  font-style: italic;
}

.column-analysis {
  margin: 16px;
}

.analysis-header {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  padding-bottom: 4px;
  border-bottom: 1px solid var(--border-light);
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.column-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-medium);
  padding: 12px;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.column-name {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-primary);
}

.column-type {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.type-email { background: #fff3e0; color: #e65100; }
.type-phone { background: #e8f5e8; color: #2e7d32; }
.type-date { background: #f3e5f5; color: #7b1fa2; }
.type-number { background: #e3f2fd; color: #1565c0; }
.type-text { background: #f5f5f5; color: #424242; }
.type-category { background: #fff8e1; color: #f57f17; }
.type-empty { background: #ffebee; color: #c62828; }

.column-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 11px;
  color: var(--text-secondary);
}

.stat-value {
  font-size: 11px;
  font-weight: 500;
  color: var(--text-primary);
}

.loading-spinner {
  width: 12px;
  height: 12px;
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}