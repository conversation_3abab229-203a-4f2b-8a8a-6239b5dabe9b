.file-analyzer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.file-analyzer-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 1200px;
  height: 90%;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.file-analyzer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.file-analyzer-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 24px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.file-analyzer-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.tab {
  flex: 1;
  padding: 16px 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: all 0.2s;
  border-bottom: 3px solid transparent;
}

.tab:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.tab.active {
  background: white;
  color: #667eea;
  border-bottom-color: #667eea;
}

.file-analyzer-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

/* Overview Tab */
.file-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #e0e0e0;
}

.stat-icon {
  font-size: 2rem;
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

.data-health {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
}

.data-health h3 {
  margin: 0 0 16px 0;
  color: #333;
}

.health-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.health-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.health-metric .warning {
  color: #ff6b35;
  font-weight: 600;
}

.health-metric .good {
  color: #28a745;
  font-weight: 600;
}

.health-metric .info {
  color: #667eea;
  font-weight: 600;
}

/* Columns Tab */
.columns-header {
  margin-bottom: 20px;
}

.columns-header h3 {
  margin: 0;
  color: #333;
}

.columns-list {
  display: grid;
  gap: 16px;
}

.column-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  transition: box-shadow 0.2s;
}

.column-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.column-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.column-icon {
  font-size: 1.2rem;
}

.name {
  font-weight: 600;
  color: #333;
}

.type-badge {
  background: #667eea;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.column-health {
  display: flex;
  align-items: center;
  gap: 8px;
}

.health-bar {
  width: 60px;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  position: relative;
}

.health-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: #666;
}

.column-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.stat {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
}

.stat span:first-child {
  color: #666;
}

.stat span:last-child {
  font-weight: 600;
  color: #333;
}

.column-samples {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.samples-label {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 8px;
  display: block;
}

.samples-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.sample-value {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #333;
  border: 1px solid #e0e0e0;
}

/* Preview Tab */
.preview-table-container {
  overflow: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  max-height: 500px;
}

.preview-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.preview-table th,
.preview-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.preview-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 10;
}

.row-number {
  background: #667eea !important;
  color: white !important;
  font-weight: 600;
  text-align: center;
  width: 50px;
}

.column-header-content {
  display: flex;
  align-items: center;
  gap: 6px;
}

.empty-cell {
  background: #fff3cd;
  color: #856404;
  font-style: italic;
}

.preview-table tbody tr:hover {
  background: #f8f9fa;
}

/* Issues Tab */
.no-issues {
  text-align: center;
  padding: 40px 20px;
}

.success-icon {
  font-size: 4rem;
  margin-bottom: 16px;
}

.no-issues h4 {
  color: #28a745;
  margin-bottom: 8px;
}

.no-issues p {
  color: #666;
}

.issues-list {
  margin-bottom: 24px;
}

.issue-card {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin-bottom: 12px;
}

.issue-icon {
  font-size: 1.5rem;
  margin-right: 12px;
  margin-top: 2px;
}

.issue-content {
  flex: 1;
}

.issue-text {
  font-weight: 500;
  color: #856404;
  margin-bottom: 4px;
}

.issue-suggestion {
  font-size: 0.875rem;
  color: #6c757d;
  font-style: italic;
}

.recommendations {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  padding: 20px;
}

.recommendations h4 {
  margin: 0 0 12px 0;
  color: #0066cc;
}

.recommendations ul {
  margin: 0;
  padding-left: 20px;
}

.recommendations li {
  margin-bottom: 8px;
  color: #333;
}

/* Footer */
.file-analyzer-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-top: 1px solid #e0e0e0;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

.btn-secondary {
  background: #6c757d;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-primary {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}

.btn-primary:hover {
  background: #5a67d8;
}

/* Responsive */
@media (max-width: 768px) {
  .file-analyzer-modal {
    width: 95%;
    height: 95%;
  }
  
  .file-stats {
    grid-template-columns: 1fr;
  }
  
  .column-stats {
    grid-template-columns: 1fr 1fr;
  }
  
  .file-analyzer-tabs {
    flex-wrap: wrap;
  }
  
  .tab {
    flex: 1 1 50%;
    min-width: 120px;
  }
}