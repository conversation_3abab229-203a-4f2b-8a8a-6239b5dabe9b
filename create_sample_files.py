"""
Create sample Excel files for testing column mapping
"""

import pandas as pd
from datetime import datetime, timedelta
import random

def create_employee_sample():
    """Create employee sample with various column name formats"""
    
    # Sample data with different column naming conventions
    employees_data = {
        'Full Name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        '<PERSON><PERSON> Address': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'Years': [28, 34, 31, 26, 42],
        'Dept': ['Engineering', 'Marketing', 'Sales', 'HR', 'Engineering'],
        'Annual Salary': [75000, 68000, 72000, 58000, 85000],
        'Start Date': ['2022-01-15', '2021-06-20', '2023-03-10', '2022-11-05', '2020-08-12'],
        'Contact Number': ['******-0101', '******-0102', '******-0103', '******-0104', '******-0105']
    }
    
    df = pd.DataFrame(employees_data)
    df.to_excel('sample_employees.xlsx', index=False)
    print("✅ Created sample_employees.xlsx")
    return df

def create_customer_sample():
    """Create customer sample with different column formats"""
    
    customers_data = {
        'Company': ['Tech Solutions Inc', 'Marketing Pro LLC', 'Sales Force Co', 'HR Experts', 'Dev Studios'],
        'Contact Person': ['Alice Cooper', 'Bob Martin', 'Carol White', 'Dan Green', 'Eva Black'],
        'Business Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'Phone Number': ['******-1001', '******-1002', '******-1003', '******-1004', '******-1005'],
        'Street Address': ['123 Tech St', '456 Marketing Ave', '789 Sales Blvd', '321 HR Lane', '654 Dev Road'],
        'City Name': ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'],
        'Country Code': ['USA', 'USA', 'USA', 'USA', 'USA']
    }
    
    df = pd.DataFrame(customers_data)
    df.to_excel('sample_customers.xlsx', index=False)
    print("✅ Created sample_customers.xlsx")
    return df

def create_mixed_format_sample():
    """Create a sample with challenging column names to test auto-mapping"""
    
    mixed_data = {
        'employee_name': ['Alex Turner', 'Beth Adams', 'Chris Lee', 'Diana Ross', 'Eric Stone'],
        'work_email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'age_years': [29, 35, 27, 31, 38],
        'department_name': ['IT', 'Finance', 'Operations', 'Legal', 'IT'],
        'yearly_compensation': [70000, 65000, 60000, 80000, 75000],
        'hire_date': ['2021-05-15', '2020-12-01', '2023-01-20', '2019-09-10', '2022-07-30'],
        'mobile_phone': ['555-2001', '555-2002', '555-2003', '555-2004', '555-2005']
    }
    
    df = pd.DataFrame(mixed_data)
    df.to_excel('sample_mixed_format.xlsx', index=False)
    print("✅ Created sample_mixed_format.xlsx")
    return df

def create_large_sample():
    """Create a larger sample for performance testing"""
    
    departments = ['Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 'Operations', 'Legal', 'IT']
    first_names = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Emily', 'Chris', 'Lisa', 'Mark', 'Anna']
    last_names = ['Smith', 'Johnson', 'Brown', 'Davis', 'Wilson', 'Miller', 'Moore', 'Taylor', 'Anderson', 'Thomas']
    
    large_data = []
    
    for i in range(100):  # Create 100 employee records
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        
        record = {
            'Employee Name': f'{first_name} {last_name}',
            'Work Email': f'{first_name.lower()}.{last_name.lower()}{i}@company.com',
            'Age': random.randint(22, 65),
            'Department': random.choice(departments),
            'Salary': random.randint(45000, 120000),
            'Hire Date': (datetime.now() - timedelta(days=random.randint(30, 1825))).strftime('%Y-%m-%d'),
            'Phone': f'555-{random.randint(1000, 9999)}'
        }
        large_data.append(record)
    
    df = pd.DataFrame(large_data)
    df.to_excel('sample_large_dataset.xlsx', index=False)
    print("✅ Created sample_large_dataset.xlsx (100 records)")
    return df

def create_problematic_sample():
    """Create a sample with data quality issues to test validation"""
    
    problematic_data = {
        'Name': ['Valid User', '', 'Another User', 'User With Long Name That Might Cause Issues', None],
        'Email': ['<EMAIL>', 'invalid-email', '<EMAIL>', 'toolong' + 'x'*50 + '@email.com', ''],
        'Age': [25, 'not a number', 150, -5, 30],
        'Department': ['Engineering', 'Unknown Dept', 'Sales', '', 'Marketing'],
        'Salary': [50000, 'fifty thousand', -1000, 999999999, 60000],
        'Phone': ['555-1234', '123', '******-5678', 'not a phone', '555.9999']
    }
    
    df = pd.DataFrame(problematic_data)
    df.to_excel('sample_problematic_data.xlsx', index=False)
    print("✅ Created sample_problematic_data.xlsx (with data quality issues)")
    return df

def create_readme():
    """Create a README file explaining the sample files"""
    
    readme_content = """# Sample Excel Files for Column Mapping

This directory contains various Excel files to test the column mapping functionality:

## 📁 Sample Files

### 1. `sample_employees.xlsx`
**Purpose**: Standard employee data with common column variations
**Columns**: Full Name, Email Address, Years, Dept, Annual Salary, Start Date, Contact Number
**Use Case**: Test auto-mapping with slightly different column names

### 2. `sample_customers.xlsx`
**Purpose**: Customer data for testing customer schema mapping
**Columns**: Company, Contact Person, Business Email, Phone Number, Street Address, City Name, Country Code
**Use Case**: Test mapping to customer schema

### 3. `sample_mixed_format.xlsx`
**Purpose**: Employee data with underscore naming convention
**Columns**: employee_name, work_email, age_years, department_name, yearly_compensation, hire_date, mobile_phone
**Use Case**: Test auto-mapping with different naming conventions

### 4. `sample_large_dataset.xlsx`
**Purpose**: Performance testing with 100 employee records
**Columns**: Employee Name, Work Email, Age, Department, Salary, Hire Date, Phone
**Use Case**: Test import performance and batch processing

### 5. `sample_problematic_data.xlsx`
**Purpose**: Data with quality issues for validation testing
**Columns**: Name, Email, Age, Department, Salary, Phone
**Issues**: Empty values, invalid emails, non-numeric ages, invalid salaries
**Use Case**: Test data validation and error handling

## 🎯 Testing Scenarios

### Auto-Mapping Test:
1. Upload `sample_employees.xlsx`
2. Use "Import with Column Mapping"
3. Select "employees" schema
4. Verify auto-suggestions match correctly

### Manual Mapping Test:
1. Upload `sample_mixed_format.xlsx`
2. Manually map underscore columns to schema fields
3. Test different field types (text, number, email, date)

### Validation Test:
1. Upload `sample_problematic_data.xlsx`
2. Try to map and import
3. Verify validation errors are caught and reported

### Performance Test:
1. Upload `sample_large_dataset.xlsx`
2. Test import speed with 100 records
3. Monitor processing time and memory usage

## 🚀 Expected Mappings

### Employee Schema Mappings:
- Full Name → name
- Email Address → email
- Years/Age → age
- Dept/Department → department
- Annual Salary/Salary → salary
- Start Date/Hire Date → hire_date
- Contact Number/Phone → phone

### Customer Schema Mappings:
- Company → company_name
- Contact Person → contact_name
- Business Email → email
- Phone Number → phone
- Street Address → address
- City Name → city
- Country Code → country
"""
    
    with open('SAMPLE_FILES_README.md', 'w') as f:
        f.write(readme_content)
    
    print("✅ Created SAMPLE_FILES_README.md")

def main():
    """Create all sample files"""
    print("Creating sample Excel files for column mapping testing...\n")
    
    try:
        create_employee_sample()
        create_customer_sample()
        create_mixed_format_sample()
        create_large_sample()
        create_problematic_sample()
        create_readme()
        
        print(f"\n🎉 All sample files created successfully!")
        print("\nFiles created:")
        print("- sample_employees.xlsx (5 records)")
        print("- sample_customers.xlsx (5 records)")
        print("- sample_mixed_format.xlsx (5 records)")
        print("- sample_large_dataset.xlsx (100 records)")
        print("- sample_problematic_data.xlsx (5 records with issues)")
        print("- SAMPLE_FILES_README.md (documentation)")
        
    except Exception as e:
        print(f"❌ Error creating sample files: {e}")

if __name__ == "__main__":
    main()