import pandas as pd

# Create sample data
data = {
    'Name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
    'Age': [30, 25, 35, 28, 42],
    'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
    'Department': ['Engineering', 'Marketing', 'Sales', 'HR', 'Engineering'],
    'Salary': [75000, 65000, 70000, 60000, 85000]
}

df = pd.DataFrame(data)
df.to_excel('test_data.xlsx', index=False)
print("Test Excel file created: test_data.xlsx")