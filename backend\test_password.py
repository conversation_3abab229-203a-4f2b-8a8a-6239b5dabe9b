Test different password approaches
"""
"""

import pymysql

# Test different password formats
passwords_to_test = [
    'root@123',      # Original
    'root%40123',    # URL encoded
    '',              # Empty
    'root',          # Simple
    'password',      # Common default
    '123456',        # Common default
]

host = 'localhost'
user = 'root'
database = 'excel_importer'

print("Testing different passwords...")

for password in passwords_to_test:
    try:
        print(f"\nTrying password: '{password}'")
        connection = pymysql.connect(
            host=host,
            port=3306,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        print(f"✅ SUCCESS! Password '{password}' works!")
        
        # Test a simple query
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM imported_data")
            count = cursor.fetchone()
            print(f"   Table has {count[0]} records")
        
        connection.close()
        
        # Update config.py with working password
        print(f"\n🎉 Working password found: '{password}'")
        print("Update your config.py with this password!")
        break
        
    except pymysql.Error as e:
        if "Access denied" in str(e):
            print(f"   ❌ Access denied")
        elif "Unknown database" in str(e):
            print(f"   ❌ Database doesn't exist")
        else:
            print(f"   ❌ Error: {e}")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")

else:
    print("\n❌ None of the common passwords worked.")
    print("Please check your MySQL installation and root password.")