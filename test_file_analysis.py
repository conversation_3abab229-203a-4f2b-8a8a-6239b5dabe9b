"""
Test the enhanced file analysis endpoint
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_enhanced_file_analysis():
    """Test the enhanced file analysis"""
    print("🔍 Testing Enhanced File Analysis")
    print("-" * 50)
    
    try:
        with open('sample_quality_test.xlsx', 'rb') as f:
            files = {'file': ('sample_quality_test.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = requests.post(f"{BASE_URL}/upload-excel/", files=files)
        
        if response.status_code == 200:
            file_data = response.json()
            
            print(f"📁 File: {file_data['filename']}")
            print(f"💾 Size: {file_data['file_size']:,} bytes")
            print(f"📊 Dimensions: {file_data['total_rows']} rows × {file_data['total_columns']} columns")
            
            print(f"\n📋 Column Analysis:")
            for i, col in enumerate(file_data['columns'][:5], 1):  # Show first 5 columns
                icon = {'number': '🔢', 'text': '📝', 'email': '📧', 'phone': '📞', 'date': '📅'}.get(col['data_type'], '📝')
                print(f"  {i}. {icon} {col['name']} ({col['data_type']})")
                print(f"     • {col['percentage_filled']}% filled ({col['non_empty_values']}/{col['total_values']})")
                print(f"     • {col['unique_values']} unique values")
                if col['sample_values']:
                    samples = ', '.join([str(v) for v in col['sample_values'][:3]])
                    print(f"     • Samples: {samples}")
                print()
            
            if len(file_data['columns']) > 5:
                print(f"  ... and {len(file_data['columns']) - 5} more columns")
            
            print(f"\n📈 Data Summary:")
            summary = file_data['data_summary']
            print(f"  • Empty rows: {summary['empty_rows']}")
            print(f"  • Empty columns: {summary['empty_columns']}")
            
            if summary['potential_issues']:
                print(f"\n⚠️  Potential Issues ({len(summary['potential_issues'])}):")
                for issue in summary['potential_issues']:
                    print(f"  • {issue}")
            else:
                print(f"\n✅ No issues detected!")
            
            print(f"\n🔍 Preview Data (first 3 rows):")
            for i, row in enumerate(file_data['preview_data'][:3], 1):
                print(f"  Row {i}: {len([v for v in row.values() if v is not None])} non-empty values")
            
            return True
        else:
            print(f"❌ Upload failed: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except FileNotFoundError:
        print("❌ Sample file not found. Please run create_enhanced_samples.py first.")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Enhanced File Analysis Test")
    print("=" * 60)
    
    # Check backend
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code != 200:
            print("❌ Backend not running. Please start the backend first.")
            exit(1)
    except:
        print("❌ Cannot connect to backend. Please start the backend first.")
        exit(1)
    
    print("✅ Backend is running!")
    print()
    
    success = test_enhanced_file_analysis()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Enhanced file analysis test completed successfully!")
        print("✅ The file structure analysis provides detailed insights.")
        print("📝 Check the frontend at http://localhost:3000 to see the new File Analyzer UI!")
    else:
        print("❌ Enhanced file analysis test failed.")
        print("⚠️  Check the backend logs for details.")