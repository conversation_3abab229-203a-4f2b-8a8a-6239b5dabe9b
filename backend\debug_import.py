"""
Debug the import process to see where it's getting stuck
"""

import pandas as pd
import io
import json
import uuid
from datetime import datetime
from database import init_database, ImportedData
import time

def debug_import_process():
    """Debug step by step import process"""
    
    print("=== Debug Import Process ===")
    
    # Step 1: Test database connection
    print("1. Testing database connection...")
    try:
        engine, SessionLocal = init_database()
        db = SessionLocal()
        
        from sqlalchemy import text
        result = db.execute(text("SELECT 1"))
        print("   ✅ Database connection successful")
        db.close()
    except Exception as e:
        print(f"   ❌ Database connection failed: {e}")
        return
    
    # Step 2: Create test Excel data
    print("2. Creating test Excel data...")
    test_data = {
        'Name': ['Test User 1', 'Test User 2'],
        'Age': [25, 30],
        'Email': ['<EMAIL>', '<EMAIL>']
    }
    df = pd.DataFrame(test_data)
    print(f"   ✅ Created DataFrame with {len(df)} rows")
    
    # Step 3: Test Excel processing
    print("3. Testing Excel processing...")
    try:
        # Simulate file processing
        excel_buffer = io.BytesIO()
        df.to_excel(excel_buffer, index=False)
        excel_buffer.seek(0)
        
        # Read it back
        df_read = pd.read_excel(excel_buffer)
        print(f"   ✅ Excel processing successful, read {len(df_read)} rows")
    except Exception as e:
        print(f"   ❌ Excel processing failed: {e}")
        return
    
    # Step 4: Test data conversion
    print("4. Testing data conversion...")
    try:
        session_id = str(uuid.uuid4())
        batch_records = []
        
        for index, row in df_read.iterrows():
            row_data = row.to_dict()
            # Handle NaN values
            for key, value in row_data.items():
                if pd.isna(value):
                    row_data[key] = None
            
            batch_records.append({
                'import_session_id': session_id,
                'row_data': json.dumps(row_data),
                'created_at': datetime.utcnow()
            })
        
        print(f"   ✅ Data conversion successful, prepared {len(batch_records)} records")
    except Exception as e:
        print(f"   ❌ Data conversion failed: {e}")
        return
    
    # Step 5: Test database insert
    print("5. Testing database insert...")
    try:
        start_time = time.time()
        
        engine, SessionLocal = init_database()
        db = SessionLocal()
        
        db.bulk_insert_mappings(ImportedData, batch_records)
        db.commit()
        
        end_time = time.time()
        print(f"   ✅ Database insert successful in {end_time - start_time:.2f} seconds")
        
        # Verify
        count = db.query(ImportedData).filter(ImportedData.import_session_id == session_id).count()
        print(f"   ✅ Verified {count} records in database")
        
        db.close()
        
    except Exception as e:
        print(f"   ❌ Database insert failed: {e}")
        return
    
    print("\n🎉 All steps completed successfully!")
    print("The import process should be working. Check for:")
    print("- Large file sizes")
    print("- Network connectivity")
    print("- Frontend timeout settings")

if __name__ == "__main__":
    debug_import_process()