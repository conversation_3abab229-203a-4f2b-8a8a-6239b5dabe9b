.templates-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.templates-modal {
  background: var(--bg-tertiary);
  border-radius: var(--radius-large);
  width: 90vw;
  max-width: 1200px;
  height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-heavy);
}

.templates-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
  border-radius: var(--radius-large) var(--radius-large) 0 0;
}

.templates-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  padding: 8px 12px;
  background: var(--error-red);
  color: white;
  border: none;
  border-radius: var(--radius-medium);
  cursor: pointer;
  font-size: 14px;
}

.close-btn:hover {
  background: #b91c1c;
}

.templates-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.templates-grid {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  align-content: start;
}

.template-card {
  background: var(--bg-tertiary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-large);
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.template-card:hover {
  border-color: var(--primary-blue);
  box-shadow: var(--shadow-medium);
}

.template-card.selected {
  border-color: var(--primary-blue);
  background: var(--secondary-blue);
  box-shadow: var(--shadow-medium);
}

.template-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.template-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  border-radius: var(--radius-medium);
}

.template-info {
  flex: 1;
}

.template-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.template-description {
  font-size: 12px;
  color: var(--text-secondary);
}

.template-steps {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.template-step {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-connector {
  width: 20px;
  display: flex;
  justify-content: center;
  position: relative;
}

.connector-line {
  width: 2px;
  height: 20px;
  background: var(--border-medium);
}

.step-content {
  flex: 1;
}

.step-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
}

.step-description {
  font-size: 10px;
  color: var(--text-secondary);
}

.template-details {
  width: 400px;
  background: var(--bg-secondary);
  border-left: 1px solid var(--border-light);
  padding: 20px;
  overflow-y: auto;
}

.details-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--border-light);
}

.details-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.details-section {
  background: var(--bg-tertiary);
  border-radius: var(--radius-medium);
  padding: 16px;
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.step-number {
  width: 24px;
  height: 24px;
  background: var(--primary-blue);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-details {
  flex: 1;
}

.step-title {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.step-desc {
  font-size: 11px;
  color: var(--text-secondary);
}

.config-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid var(--border-light);
}

.config-item:last-child {
  border-bottom: none;
}

.config-key {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 500;
}

.config-value {
  font-size: 11px;
  color: var(--text-primary);
  background: var(--bg-secondary);
  padding: 2px 6px;
  border-radius: 3px;
}

.templates-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-light);
  border-radius: 0 0 var(--radius-large) var(--radius-large);
}

.cancel-btn,
.use-template-btn {
  padding: 10px 20px;
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-medium);
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.cancel-btn:hover {
  background: var(--bg-secondary);
}

.use-template-btn {
  background: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
}

.use-template-btn:hover:not(:disabled) {
  background: var(--primary-hover);
}

.use-template-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}