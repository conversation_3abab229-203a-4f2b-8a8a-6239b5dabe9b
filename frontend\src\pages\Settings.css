/* Settings Page Styles */
.settings {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Settings Header */
.settings-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.header-info h1 {
  margin: 0 0 0.5rem 0;
  color: #1e293b;
  font-size: 2rem;
  font-weight: 700;
}

.header-info p {
  margin: 0;
  color: #64748b;
  font-size: 1rem;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* Settings Container */
.settings-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  align-items: start;
}

/* Settings Sidebar */
.settings-sidebar {
  position: sticky;
  top: 2rem;
}

.settings-tabs {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-tab {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  margin-bottom: 0.5rem;
}

.settings-tab:last-child {
  margin-bottom: 0;
}

.settings-tab:hover {
  background: rgba(59, 130, 246, 0.1);
  transform: translateX(4px);
}

.settings-tab.active {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.tab-icon {
  font-size: 1.25rem;
  min-width: 24px;
}

.tab-label {
  font-weight: 500;
  font-size: 0.875rem;
}

/* Settings Content */
.settings-content {
  min-height: 600px;
}

.settings-section {
  padding: 0;
}

.settings-section h3 {
  margin: 0 0 2rem 0;
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 700;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e2e8f0;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

/* Setting Items */
.setting-item {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.setting-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  margin: 0;
}

.setting-input,
.setting-textarea,
.setting-select {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.9);
}

.setting-input:focus,
.setting-textarea:focus,
.setting-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.setting-textarea {
  resize: vertical;
  min-height: 80px;
}

/* Theme Selector */
.theme-selector {
  display: flex;
  gap: 0.5rem;
}

.theme-option {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.theme-option:hover {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.theme-option.active {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

/* Color Picker */
.color-picker {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.color-picker input[type="color"] {
  width: 50px;
  height: 40px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  background: none;
}

.color-value {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

/* Toggle Switch */
.toggle-switch {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.toggle-switch input[type="checkbox"] {
  position: relative;
  width: 50px;
  height: 26px;
  appearance: none;
  background: #cbd5e1;
  border-radius: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-switch input[type="checkbox"]:checked {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.toggle-switch input[type="checkbox"]::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 22px;
  height: 22px;
  background: white;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch input[type="checkbox"]:checked::before {
  transform: translateX(24px);
}

.toggle-switch label {
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .settings-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .settings-sidebar {
    position: static;
  }
  
  .settings-tabs {
    display: flex;
    overflow-x: auto;
    padding: 0.5rem;
  }
  
  .settings-tab {
    flex-shrink: 0;
    margin-right: 0.5rem;
    margin-bottom: 0;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .header-actions {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .theme-selector {
    flex-direction: column;
  }
  
  .color-picker {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .settings {
    gap: 1rem;
  }
  
  .settings-header {
    padding: 1.5rem;
  }
}

/* Animation for content changes */
.settings-content .card {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Success state for save button */
.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.btn .spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}