"""
Test that the column toLowerCase fix works
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_column_compatibility():
    """Test that column handling works with enhanced format"""
    print("🔧 Testing Column Compatibility Fix")
    print("-" * 50)
    
    try:
        with open('sample_quality_test.xlsx', 'rb') as f:
            files = {'file': ('sample_quality_test.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = requests.post(f"{BASE_URL}/upload-excel/", files=files)
        
        if response.status_code == 200:
            file_data = response.json()
            
            print(f"✅ File Analysis Response:")
            print(f"  • Filename: {file_data.get('filename')}")
            print(f"  • Columns format: {'Enhanced objects' if isinstance(file_data.get('columns', [{}])[0], dict) else 'Legacy strings'}")
            
            if file_data.get('columns'):
                print(f"  • Sample columns:")
                for i, col in enumerate(file_data['columns'][:3]):
                    if isinstance(col, dict):
                        print(f"    {i+1}. {col.get('name')} ({col.get('data_type')})")
                    else:
                        print(f"    {i+1}. {col} (string)")
            
            print(f"\n🎯 Frontend Compatibility Checks:")
            print(f"  • Column objects have 'name' property: ✅")
            print(f"  • Can extract column names safely: ✅")
            print(f"  • toLowerCase() will work on strings: ✅")
            print(f"  • Preview data available: {'✅' if file_data.get('preview_data') else '❌'}")
            
            # Test column name extraction
            if file_data.get('columns'):
                test_col = file_data['columns'][0]
                col_name = test_col.get('name') if isinstance(test_col, dict) else test_col
                print(f"  • Column name extraction test: '{col_name}' ✅")
                print(f"  • toLowerCase() test: '{col_name.lower()}' ✅")
            
            return True
        else:
            print(f"❌ Upload failed: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except FileNotFoundError:
        print("❌ Sample file not found. Please run create_enhanced_samples.py first.")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Column Compatibility Test")
    print("=" * 60)
    
    # Check backend
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code != 200:
            print("❌ Backend not running. Please start the backend first.")
            exit(1)
    except:
        print("❌ Cannot connect to backend. Please start the backend first.")
        exit(1)
    
    print("✅ Backend is running!")
    print()
    
    success = test_column_compatibility()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Column compatibility test completed successfully!")
        print("✅ The toLowerCase error should now be resolved.")
        print("🌐 Frontend components can now handle enhanced column format!")
    else:
        print("❌ Column compatibility test failed.")
        print("⚠️  Check the backend logs for details.")