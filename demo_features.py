"""
Demo script to showcase enhanced ETL features
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def demo_pipeline_templates():
    """Demo the pipeline templates feature"""
    print("🎯 Demo: Pipeline Templates")
    print("-" * 40)
    
    response = requests.get(f"{BASE_URL}/api/pipeline-templates")
    if response.status_code == 200:
        templates = response.json()['templates']
        print(f"Available Templates: {len(templates)}")
        
        for i, template in enumerate(templates, 1):
            print(f"\n{i}. {template['name']}")
            print(f"   Description: {template['description']}")
            print(f"   Fields: {len(template['fields'])}")
            
            # Show first few fields
            for field in template['fields'][:3]:
                required = "Required" if field['required'] else "Optional"
                print(f"   - {field['name']} ({field['type']}) - {required}")
            
            if len(template['fields']) > 3:
                print(f"   ... and {len(template['fields']) - 3} more fields")
    
    print("\n✅ Pipeline templates loaded successfully!")
    return True

def demo_file_upload_and_analysis():
    """Demo file upload and enhanced analysis"""
    print("\n🎯 Demo: File Upload & Analysis")
    print("-" * 40)
    
    # Upload sample file
    try:
        with open('sample_quality_test.xlsx', 'rb') as f:
            files = {'file': ('sample_quality_test.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = requests.post(f"{BASE_URL}/upload-excel/", files=files)
        
        if response.status_code == 200:
            data = response.json()
            print(f"📁 File: {data['filename']}")
            print(f"📊 Rows: {data['rows']}")
            print(f"📋 Columns: {len(data['columns'])}")
            print(f"🔍 Preview: {len(data['preview'])} sample records")
            
            print(f"\nColumns detected:")
            for i, col in enumerate(data['columns'], 1):
                print(f"  {i}. {col}")
            
            print("\n✅ File upload and preview successful!")
            return True
        else:
            print(f"❌ Upload failed: {response.status_code}")
            return False
            
    except FileNotFoundError:
        print("❌ Sample file not found. Please run create_enhanced_samples.py first.")
        return False
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return False

def demo_import_and_validation():
    """Demo data import and quality validation"""
    print("\n🎯 Demo: Data Import & Quality Validation")
    print("-" * 40)
    
    # Import data
    try:
        with open('sample_quality_test.xlsx', 'rb') as f:
            files = {'file': ('sample_quality_test.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = requests.post(f"{BASE_URL}/import-data/", files=files)
        
        if response.status_code == 200:
            import_result = response.json()
            session_id = import_result['session_id']
            
            print(f"📥 Import completed!")
            print(f"🆔 Session ID: {session_id[:8]}...")
            print(f"📊 Imported: {import_result['imported_rows']} rows")
            print(f"⏱️  Time: {import_result['processing_time']}")
            
            # Now test data validation
            print(f"\n🔍 Running data quality validation...")
            validation_response = requests.get(f"{BASE_URL}/api/validate/{session_id}")
            
            if validation_response.status_code == 200:
                quality_data = validation_response.json()
                
                print(f"\n📈 Quality Score: {quality_data['quality_score']}/100")
                print(f"🔢 Total Issues: {len(quality_data['issues'])}")
                
                # Show issue summary
                summary = quality_data['summary']
                if summary['missing_values'] > 0:
                    print(f"   • Missing values: {summary['missing_values']}")
                if summary['duplicate_records'] > 0:
                    print(f"   • Duplicate records: {summary['duplicate_records']}")
                if summary['data_type_issues'] > 0:
                    print(f"   • Data type issues: {summary['data_type_issues']}")
                if summary['format_issues'] > 0:
                    print(f"   • Format issues: {summary['format_issues']}")
                if summary['outliers'] > 0:
                    print(f"   • Outliers: {summary['outliers']}")
                
                # Show top issues
                print(f"\n🚨 Top Issues:")
                for issue in quality_data['issues'][:3]:
                    severity_icon = "🔴" if issue['severity'] == 'high' else "🟡" if issue['severity'] == 'medium' else "🟢"
                    print(f"   {severity_icon} {issue['description']}")
                
                # Show recommendations
                if quality_data['recommendations']:
                    print(f"\n💡 Recommendations:")
                    for rec in quality_data['recommendations']:
                        print(f"   • {rec}")
                
                print("\n✅ Data quality validation completed!")
                return session_id
            else:
                print(f"❌ Validation failed: {validation_response.status_code}")
                return session_id
        else:
            print(f"❌ Import failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Import error: {e}")
        return None

def demo_enhanced_preview(session_id):
    """Demo enhanced preview feature"""
    if not session_id:
        print("⚠️  Skipping enhanced preview - no session ID")
        return
    
    print("\n🎯 Demo: Enhanced Preview")
    print("-" * 40)
    
    response = requests.get(f"{BASE_URL}/api/enhanced-preview/{session_id}")
    
    if response.status_code == 200:
        preview_data = response.json()
        
        print(f"📊 Dataset Overview:")
        print(f"   • Total records: {preview_data['total_records']}")
        print(f"   • Sample shown: {len(preview_data['sample_data'])}")
        
        print(f"\n📋 Column Statistics:")
        for col, stats in list(preview_data['column_stats'].items())[:5]:
            if stats['type'] == 'numeric':
                print(f"   📈 {col}: {stats['count']} values, avg: {stats.get('mean', 'N/A'):.2f}")
            else:
                print(f"   📝 {col}: {stats['count']} values, {stats['unique']} unique")
        
        if len(preview_data['column_stats']) > 5:
            print(f"   ... and {len(preview_data['column_stats']) - 5} more columns")
        
        print(f"\n📊 Charts Available:")
        for col, chart in list(preview_data['charts_data'].items())[:3]:
            print(f"   📊 {col}: {chart['type']} chart with {len(chart['data'])} data points")
        
        print("\n✅ Enhanced preview generated successfully!")
    else:
        print(f"❌ Enhanced preview failed: {response.status_code}")

def main():
    """Run the complete demo"""
    print("🚀 ETL Interface Enhanced Features Demo")
    print("=" * 50)
    
    # Check if backend is running
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code != 200:
            print("❌ Backend not running. Please start the backend first.")
            return
    except:
        print("❌ Cannot connect to backend. Please start the backend first.")
        return
    
    print("✅ Backend is running!")
    
    # Run demos
    demos = [
        ("Pipeline Templates", demo_pipeline_templates),
        ("File Upload & Analysis", demo_file_upload_and_analysis),
    ]
    
    session_id = None
    
    for demo_name, demo_func in demos:
        print(f"\n{'='*20}")
        result = demo_func()
        if demo_name == "Data Import & Quality Validation":
            session_id = result
        time.sleep(1)  # Small delay between demos
    
    # Import and validation demo
    print(f"\n{'='*20}")
    session_id = demo_import_and_validation()
    
    # Enhanced preview demo
    if session_id:
        print(f"\n{'='*20}")
        demo_enhanced_preview(session_id)
    
    print(f"\n{'='*50}")
    print("🎉 Demo completed!")
    print("\n📝 Next steps:")
    print("1. Open http://localhost:3000 in your browser")
    print("2. Try uploading the sample files manually")
    print("3. Test the enhanced features in the UI")
    print("4. Use the testing guide for comprehensive testing")

if __name__ == "__main__":
    main()