"""
Check what DATABASE_URL is being used
"""

from config import DATABASE_URL, DB_CONFIG

print("=== Database Configuration Debug ===")
print(f"DB_CONFIG: {DB_CONFIG}")
print(f"Generated DATABASE_URL: {DATABASE_URL}")

# Test the URL manually
import pymysql
from urllib.parse import urlparse

try:
    # Parse the URL
    parsed = urlparse(DATABASE_URL.replace('mysql+pymysql://', 'mysql://'))
    print(f"\nParsed URL components:")
    print(f"Host: {parsed.hostname}")
    print(f"Port: {parsed.port}")
    print(f"Username: {parsed.username}")
    print(f"Password: {parsed.password}")
    print(f"Database: {parsed.path[1:]}")  # Remove leading /
    
    # Test direct connection
    connection = pymysql.connect(
        host=parsed.hostname,
        port=parsed.port or 3306,
        user=parsed.username,
        password=parsed.password,
        database=parsed.path[1:],
        charset='utf8mb4'
    )
    
    print("\n✅ Direct PyMySQL connection successful!")
    connection.close()
    
    # Test SQLAlchemy connection
    from sqlalchemy import create_engine, text
    engine = create_engine(DATABASE_URL)
    
    with engine.connect() as conn:
        result = conn.execute(text("SELECT 1"))
        print("✅ SQLAlchemy connection successful!")
    
except Exception as e:
    print(f"\n❌ Connection failed: {e}")
    print(f"Error type: {type(e)}")