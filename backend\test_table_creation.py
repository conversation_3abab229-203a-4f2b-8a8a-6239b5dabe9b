"""
Test the dynamic table creation functionality
"""

import urllib.request
import urllib.parse
import json
import os

def test_table_creation():
    """Test creating a new table from Excel"""
    
    print("Testing dynamic table creation...\n")
    
    # Test 1: Check if we can get existing tables
    print("1. Getting existing tables...")
    try:
        with urllib.request.urlopen('http://localhost:8000/tables/') as response:
            data = json.loads(response.read().decode())
            print(f"✅ Found {len(data['tables'])} existing tables:")
            for table in data['tables']:
                print(f"   - {table['name']}: {table['description']} ({table['field_count']} fields)")
    except Exception as e:
        print(f"❌ Error getting tables: {e}")
        return
    
    print("\n2. Table creation endpoints are ready!")
    print("\nTo test table creation:")
    print("1. Start the React frontend")
    print("2. Upload any sample Excel file")
    print("3. Click 'Create New Table'")
    print("4. Enter a table name (e.g., 'my_custom_data')")
    print("5. Review the field preview")
    print("6. Click 'Create Table & Import'")
    
    print("\nThis will:")
    print("- Create a new database table with proper field types")
    print("- Create a schema definition for future use")
    print("- Import your Excel data directly to the new table")
    
    print("\n🎉 Dynamic table creation is ready to test!")

if __name__ == "__main__":
    test_table_creation()