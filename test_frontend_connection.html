<!DOCTYPE html>
<html>
<head>
    <title>Test Frontend Connection</title>
</head>
<body>
    <h1>Test Excel Import</h1>
    
    <div>
        <input type="file" id="fileInput" accept=".xlsx,.xls">
        <button onclick="testUpload()">Test Upload</button>
        <button onclick="testImport()">Test Import</button>
    </div>
    
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>
    
    <script>
        function showResult(message, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = message;
            resultDiv.style.backgroundColor = isError ? '#ffebee' : '#e8f5e8';
            resultDiv.style.color = isError ? '#c62828' : '#2e7d32';
        }
        
        async function testUpload() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('Please select a file first', true);
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                showResult('Testing upload...');
                
                const response = await fetch('http://localhost:8000/upload-excel/', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                showResult(`✅ Upload successful: ${JSON.stringify(result, null, 2)}`);
                
            } catch (error) {
                showResult(`❌ Upload failed: ${error.message}`, true);
            }
        }
        
        async function testImport() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('Please select a file first', true);
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                showResult('Testing import...');
                
                const response = await fetch('http://localhost:8000/import-data/', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                showResult(`✅ Import successful: ${JSON.stringify(result, null, 2)}`);
                
            } catch (error) {
                showResult(`❌ Import failed: ${error.message}`, true);
            }
        }
    </script>
</body>
</html>