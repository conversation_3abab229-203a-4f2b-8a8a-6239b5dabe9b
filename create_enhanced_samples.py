"""
Create enhanced sample Excel files for testing new features
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random
import string

def create_quality_test_data():
    """Create data with various quality issues for testing validator"""
    
    data = []
    
    # Good quality records
    for i in range(50):
        data.append({
            'ID': f'EMP{i+1:03d}',
            'Full Name': random.choice(['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']),
            'Email': f'user{i+1}@company.com',
            'Phone': f'******-{random.randint(1000, 9999)}',
            'Age': random.randint(22, 65),
            'Department': random.choice(['Engineering', 'Marketing', 'Sales', 'HR', 'Finance']),
            'Salary': random.randint(45000, 120000),
            'Hire Date': (datetime.now() - timedelta(days=random.randint(30, 1825))).strftime('%Y-%m-%d'),
            'Status': 'Active'
        })
    
    # Add quality issues
    for i in range(50, 100):
        # Various data quality issues
        issues = {
            'ID': f'EMP{i+1:03d}' if random.random() > 0.1 else '',  # 10% missing IDs
            'Full Name': random.choice(['<PERSON>', '<PERSON> Doe', '', '   ', 'A' * 100]) if random.random() > 0.05 else None,  # Various name issues
            'Email': random.choice([
                f'user{i+1}@company.com',
                'invalid-email',
                'user@',
                '@company.com',
                'user.email.com',
                ''
            ]),
            'Phone': random.choice([
                f'******-{random.randint(1000, 9999)}',
                '123',
                'not-a-phone',
                '******-ABCD',
                ''
            ]),
            'Age': random.choice([
                random.randint(22, 65),
                -5,  # Invalid age
                150,  # Invalid age
                'twenty-five',  # Non-numeric
                None
            ]),
            'Department': random.choice(['Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 'Unknown', '', None]),
            'Salary': random.choice([
                random.randint(45000, 120000),
                -1000,  # Negative salary
                'fifty thousand',  # Non-numeric
                *********,  # Unrealistic salary
                None
            ]),
            'Hire Date': random.choice([
                (datetime.now() - timedelta(days=random.randint(30, 1825))).strftime('%Y-%m-%d'),
                '2025-13-45',  # Invalid date
                'yesterday',  # Non-date
                ''
            ]),
            'Status': random.choice(['Active', 'Inactive', 'Terminated', '', None])
        }
        data.append(issues)
    
    df = pd.DataFrame(data)
    df.to_excel('sample_quality_test.xlsx', index=False)
    print("✅ Created sample_quality_test.xlsx (100 records with quality issues)")
    return df

def create_large_dataset():
    """Create a larger dataset for performance testing"""
    
    departments = ['Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 'Operations', 'Legal', 'IT', 'Support', 'Research']
    locations = ['New York', 'San Francisco', 'Chicago', 'Austin', 'Seattle', 'Boston', 'Denver', 'Atlanta']
    
    data = []
    
    for i in range(1000):  # 1000 records
        first_name = random.choice(['John', 'Jane', 'Michael', 'Sarah', 'David', 'Emily', 'Chris', 'Lisa', 'Mark', 'Anna', 'Robert', 'Maria'])
        last_name = random.choice(['Smith', 'Johnson', 'Brown', 'Davis', 'Wilson', 'Miller', 'Moore', 'Taylor', 'Anderson', 'Thomas'])
        
        data.append({
            'Employee_ID': f'EMP{i+1:04d}',
            'First_Name': first_name,
            'Last_Name': last_name,
            'Full_Name': f'{first_name} {last_name}',
            'Email': f'{first_name.lower()}.{last_name.lower()}{i+1}@company.com',
            'Personal_Email': f'{first_name.lower()}{random.randint(100, 999)}@gmail.com',
            'Phone_Work': f'******-{random.randint(1000, 9999)}',
            'Phone_Mobile': f'+1-{random.randint(200, 999)}-{random.randint(100, 999)}-{random.randint(1000, 9999)}',
            'Age': random.randint(22, 65),
            'Birth_Date': (datetime.now() - timedelta(days=random.randint(22*365, 65*365))).strftime('%Y-%m-%d'),
            'Gender': random.choice(['Male', 'Female', 'Other']),
            'Department': random.choice(departments),
            'Job_Title': random.choice(['Manager', 'Senior', 'Junior', 'Lead', 'Director', 'Analyst', 'Specialist']),
            'Location': random.choice(locations),
            'Salary': random.randint(35000, 200000),
            'Bonus': random.randint(0, 50000),
            'Hire_Date': (datetime.now() - timedelta(days=random.randint(30, 3650))).strftime('%Y-%m-%d'),
            'Years_Experience': random.randint(0, 20),
            'Performance_Rating': random.choice(['Excellent', 'Good', 'Satisfactory', 'Needs Improvement']),
            'Manager_ID': f'EMP{random.randint(1, 100):04d}',
            'Status': random.choice(['Active', 'Active', 'Active', 'Active', 'Inactive', 'On Leave']),  # Mostly active
            'Notes': random.choice(['', 'High performer', 'New hire', 'Promotion candidate', 'Training needed', ''])
        })
    
    df = pd.DataFrame(data)
    df.to_excel('sample_large_performance.xlsx', index=False)
    print("✅ Created sample_large_performance.xlsx (1000 records for performance testing)")
    return df

def create_mixed_data_types():
    """Create data with various data types for testing type detection"""
    
    data = []
    
    for i in range(200):
        data.append({
            # Text fields
            'Product_Name': f'Product {chr(65 + i % 26)}{i+1}',
            'Description': f'This is a description for product {i+1} with various features and benefits.',
            'Category': random.choice(['Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports']),
            
            # Numeric fields
            'Price': round(random.uniform(9.99, 999.99), 2),
            'Quantity': random.randint(0, 1000),
            'Weight_KG': round(random.uniform(0.1, 50.0), 2),
            'Rating': round(random.uniform(1.0, 5.0), 1),
            
            # Date fields
            'Created_Date': (datetime.now() - timedelta(days=random.randint(1, 365))).strftime('%Y-%m-%d'),
            'Modified_Date': (datetime.now() - timedelta(days=random.randint(1, 30))).strftime('%Y-%m-%d %H:%M:%S'),
            
            # Boolean-like fields
            'In_Stock': random.choice(['Yes', 'No', 'True', 'False', 1, 0]),
            'Featured': random.choice([True, False]),
            'On_Sale': random.choice(['Y', 'N']),
            
            # Mixed/Complex fields
            'SKU': f'SKU-{random.randint(10000, 99999)}-{chr(65 + i % 26)}',
            'Barcode': f'{random.randint(100000000000, *********999)}',
            'Supplier_Code': random.choice(['SUP001', 'SUP002', 'SUP003', None, '']),
            
            # JSON-like text
            'Attributes': f'{{"color": "{random.choice(["Red", "Blue", "Green"])}", "size": "{random.choice(["S", "M", "L"])}"}}',
            
            # URLs
            'Image_URL': f'https://example.com/images/product_{i+1}.jpg',
            'Product_URL': f'https://shop.example.com/product/{i+1}',
            
            # Currency-like
            'Cost_USD': f'${round(random.uniform(5.0, 500.0), 2)}',
            'Tax_Rate': f'{round(random.uniform(5.0, 15.0), 2)}%'
        })
    
    df = pd.DataFrame(data)
    df.to_excel('sample_mixed_datatypes.xlsx', index=False)
    print("✅ Created sample_mixed_datatypes.xlsx (200 records with various data types)")
    return df

def create_financial_data():
    """Create financial transaction data for testing"""
    
    data = []
    
    for i in range(500):
        transaction_date = datetime.now() - timedelta(days=random.randint(1, 365))
        
        data.append({
            'Transaction_ID': f'TXN{i+1:06d}',
            'Account_Number': f'ACC{random.randint(100000, 999999)}',
            'Customer_ID': f'CUST{random.randint(1000, 9999)}',
            'Transaction_Date': transaction_date.strftime('%Y-%m-%d'),
            'Transaction_Time': transaction_date.strftime('%H:%M:%S'),
            'Amount': round(random.uniform(-5000.0, 10000.0), 2),
            'Currency': random.choice(['USD', 'EUR', 'GBP', 'CAD']),
            'Transaction_Type': random.choice(['Deposit', 'Withdrawal', 'Transfer', 'Payment', 'Fee']),
            'Description': random.choice([
                'ATM Withdrawal',
                'Online Purchase',
                'Direct Deposit',
                'Wire Transfer',
                'Check Deposit',
                'Service Fee',
                'Interest Payment'
            ]),
            'Merchant': random.choice(['Amazon', 'Walmart', 'Target', 'Starbucks', 'Shell', 'ATM', None]),
            'Category': random.choice(['Shopping', 'Food', 'Gas', 'Entertainment', 'Bills', 'Income', 'Fees']),
            'Balance_After': round(random.uniform(100.0, 50000.0), 2),
            'Status': random.choice(['Completed', 'Pending', 'Failed', 'Cancelled']),
            'Reference_Number': f'REF{random.randint(100000000, *********)}',
            'Branch_Code': random.choice(['BR001', 'BR002', 'BR003', 'ONLINE', 'ATM']),
            'Notes': random.choice(['', 'Recurring payment', 'Large transaction', 'International', ''])
        })
    
    df = pd.DataFrame(data)
    df.to_excel('sample_financial_transactions.xlsx', index=False)
    print("✅ Created sample_financial_transactions.xlsx (500 financial transactions)")
    return df

if __name__ == "__main__":
    print("🚀 Creating enhanced sample datasets...")
    print()
    
    # Create all sample files
    create_quality_test_data()
    create_large_dataset()
    create_mixed_data_types()
    create_financial_data()
    
    print()
    print("✅ All enhanced sample files created successfully!")
    print()
    print("📁 Files created:")
    print("  • sample_quality_test.xlsx - 100 records with data quality issues")
    print("  • sample_large_performance.xlsx - 1000 records for performance testing")
    print("  • sample_mixed_datatypes.xlsx - 200 records with various data types")
    print("  • sample_financial_transactions.xlsx - 500 financial transaction records")
    print()
    print("🎯 Use these files to test:")
    print("  • Data quality validation")
    print("  • Performance with large datasets")
    print("  • Data type detection and mapping")
    print("  • Financial data processing")