"""
Start the FastAPI server with proper initialization
"""

import uvicorn
from database import init_database

def start_server():
    """Start server with database initialization"""
    
    print("Initializing database connection...")
    try:
        engine, SessionLocal = init_database()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        print("Please check your database configuration in config.py")
        return
    
    print("Starting FastAPI server...")
    print("Server will be available at: http://localhost:8000")
    print("API documentation at: http://localhost:8000/docs")
    print("Press Ctrl+C to stop the server")
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

if __name__ == "__main__":
    start_server()