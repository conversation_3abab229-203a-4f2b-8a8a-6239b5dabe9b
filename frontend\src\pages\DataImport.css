/* Data Import Styles */
.data-import {
  max-width: 1000px;
  margin: 0 auto;
}

/* Upload Section */
.upload-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
}

.upload-card {
  width: 100%;
  max-width: 600px;
}

.upload-zone {
  border: 3px dashed rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 4rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.upload-zone::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.upload-zone:hover::before {
  transform: translateX(100%);
}

.upload-zone:hover,
.upload-zone.drag-active {
  border-color: rgba(59, 130, 246, 0.6);
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(59, 130, 246, 0.2);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.upload-icon {
  font-size: 5rem;
  opacity: 0.8;
  margin-bottom: 1rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

.upload-zone:hover .upload-icon {
  transform: scale(1.1);
  opacity: 1;
}

.upload-zone h3 {
  margin: 0;
  color: #1e293b;
  font-weight: 600;
  font-size: 1.25rem;
}

.upload-zone p {
  margin: 0;
  color: #64748b;
  font-size: 1rem;
}

.supported-formats {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.format-tag {
  background: #e2e8f0;
  color: #475569;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Processing Section */
.processing-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.file-info-card {
  width: 100%;
}

.file-details {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.file-icon {
  font-size: 3rem;
  padding: 1rem;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-meta {
  flex: 1;
}

.file-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.25rem 0;
}

.file-size {
  font-size: 0.875rem;
  color: #64748b;
  margin: 0;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  text-align: center;
}

.loading-state p {
  color: #64748b;
  margin: 0;
}

/* Error State */
.error-state {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  color: #dc2626;
}

.error-icon {
  font-size: 1.5rem;
}

.error-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  font-weight: 600;
}

.error-content p {
  margin: 0;
  font-size: 0.875rem;
}

/* Analysis Results */
.analysis-results {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.stat-label {
  display: block;
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  color: #1e293b;
  font-weight: 700;
}

/* Columns Section */
.columns-section h4 {
  margin: 0 0 1rem 0;
  color: #1e293b;
  font-weight: 600;
}

.columns-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
}

.column-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f1f5f9;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.column-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
}

.column-type {
  font-size: 0.75rem;
  color: #64748b;
  background: #e2e8f0;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

/* Import Options Section */
.import-options-section {
  margin-top: 2rem;
}

.import-options-section h4 {
  margin: 0 0 1.5rem 0;
  color: #1e293b;
  font-weight: 700;
  font-size: 1.25rem;
  text-align: center;
}

.import-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.import-method-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.import-method-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  transition: all 0.3s ease;
}

.import-method-card.primary::before {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.import-method-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

.import-method-card:hover::before {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.method-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem auto;
  transition: all 0.3s ease;
}

.import-method-card.primary .method-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.import-method-card:hover .method-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.method-content h5 {
  margin: 0 0 0.5rem 0;
  color: #1e293b;
  font-weight: 700;
  font-size: 1.125rem;
}

.method-content p {
  margin: 0 0 1.5rem 0;
  color: #64748b;
  font-size: 0.875rem;
  line-height: 1.5;
}

.method-content .btn {
  width: 100%;
  justify-content: center;
}

/* Result Card */
.result-card {
  width: 100%;
}

.success-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.success-icon {
  font-size: 3rem;
}

.success-details h3 {
  margin: 0 0 0.25rem 0;
  color: #1e293b;
  font-weight: 700;
}

.success-details p {
  margin: 0;
  color: #64748b;
}

.result-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.result-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.result-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.result-value {
  font-size: 0.875rem;
  color: #1e293b;
  font-weight: 600;
}

.result-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
  .upload-zone {
    padding: 2rem 1rem;
  }
  
  .file-details {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-row {
    grid-template-columns: 1fr;
  }
  
  .columns-grid {
    grid-template-columns: 1fr;
  }
  
  .result-actions {
    flex-direction: column;
  }
}/* A
dvanced Method Card */
.advanced-method-card {
  width: 100%;
  margin-top: 2rem;
}

.advanced-method-card .card-body {
  padding: 2rem;
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .import-methods {
    grid-template-columns: 1fr;
  }
  
  .import-method-card {
    padding: 1.5rem;
  }
  
  .method-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }
}