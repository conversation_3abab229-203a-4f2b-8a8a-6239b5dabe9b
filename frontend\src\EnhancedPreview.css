.enhanced-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.enhanced-preview {
  background: var(--bg-tertiary);
  border-radius: var(--radius-large);
  width: 90vw;
  height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-heavy);
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
  border-radius: var(--radius-large) var(--radius-large) 0 0;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.stats-toggle,
.export-btn,
.close-btn {
  padding: 6px 12px;
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-medium);
  background: var(--bg-tertiary);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.stats-toggle:hover,
.export-btn:hover {
  background: var(--secondary-blue);
  border-color: var(--primary-blue);
}

.close-btn {
  background: var(--error-red);
  color: white;
  border-color: var(--error-red);
}

.close-btn:hover {
  background: #b91c1c;
}

.statistics-panel {
  padding: 16px 20px;
  background: #f8fafc;
  border-bottom: 1px solid var(--border-light);
  max-height: 200px;
  overflow-y: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.stat-card {
  background: white;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-medium);
  padding: 12px;
}

.stat-header {
  font-weight: 600;
  font-size: 12px;
  color: var(--text-primary);
  margin-bottom: 8px;
  border-bottom: 1px solid var(--border-light);
  padding-bottom: 4px;
}

.stat-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
}

.stat-item span:first-child {
  color: var(--text-secondary);
}

.stat-item span:last-child {
  color: var(--text-primary);
  font-weight: 500;
}

.preview-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-select,
.filter-input {
  padding: 6px 10px;
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-medium);
  font-size: 12px;
}

.filter-select {
  min-width: 150px;
}

.filter-input {
  min-width: 200px;
}

.filter-btn,
.clear-btn {
  padding: 6px 12px;
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-medium);
  background: var(--bg-tertiary);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.filter-btn {
  background: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
}

.filter-btn:hover {
  background: var(--primary-hover);
}

.clear-btn:hover {
  background: var(--bg-secondary);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-btn {
  padding: 6px 12px;
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-medium);
  background: var(--bg-tertiary);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: var(--secondary-blue);
  border-color: var(--primary-blue);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 12px;
  color: var(--text-secondary);
}

.preview-table-container {
  flex: 1;
  overflow: auto;
  padding: 0 20px 20px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 12px;
  color: var(--text-secondary);
}

.enhanced-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.sortable-header {
  background: var(--bg-secondary);
  padding: 12px 8px;
  text-align: left;
  border-bottom: 2px solid var(--border-medium);
  cursor: pointer;
  user-select: none;
  position: sticky;
  top: 0;
  z-index: 10;
}

.sortable-header:hover {
  background: var(--secondary-blue);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  color: var(--text-primary);
}

.sort-indicators {
  display: flex;
  flex-direction: column;
  margin-left: 4px;
}

.sort-arrow {
  font-size: 8px;
  color: var(--text-tertiary);
  line-height: 1;
}

.sort-arrow.active {
  color: var(--primary-blue);
}

.column-stats {
  margin-top: 4px;
  font-size: 10px;
  font-weight: normal;
}

.null-indicator {
  background: var(--warning-yellow);
  color: white;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
}

.enhanced-table td {
  padding: 8px;
  border-bottom: 1px solid var(--border-light);
  color: var(--text-secondary);
}

.enhanced-table tr:hover {
  background: var(--bg-secondary);
}

.null-cell {
  background: #fef2f2;
}

.null-value {
  color: var(--text-tertiary);
  font-style: italic;
  font-size: 10px;
}