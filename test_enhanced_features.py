#!/usr/bin/env python3
"""
Comprehensive test script for all enhanced ETL features
Tests the complete pipeline with our sample data
"""

import requests
import json
import time
import os
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_health():
    """Test if backend is healthy"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Backend is healthy")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to backend: {e}")
        return False

def test_file_upload(filename):
    """Test file upload and analysis"""
    print(f"\n🔄 Testing file upload: {filename}")
    
    if not os.path.exists(filename):
        print(f"❌ File not found: {filename}")
        return None
    
    try:
        with open(filename, 'rb') as f:
            files = {'file': (filename, f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = requests.post(f"{BASE_URL}/upload-excel/", files=files)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ File uploaded successfully")
            print(f"   - Rows: {data.get('rows', 'N/A')}")
            print(f"   - Columns: {len(data.get('columns', []))}")
            print(f"   - File size: {data.get('file_size', 'N/A')} bytes")
            return data
        else:
            print(f"❌ Upload failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return None

def test_data_validation(session_id):
    """Test data quality validation"""
    print(f"\n🔄 Testing data validation...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/validate/{session_id}")
        
        if response.status_code == 200:
            validation = response.json()
            print(f"✅ Data validation completed")
            print(f"   - Quality Score: {validation.get('quality_score', 'N/A')}/100")
            print(f"   - Issues Found: {len(validation.get('issues', []))}")
            print(f"   - Recommendations: {len(validation.get('recommendations', []))}")
            
            # Show top issues
            issues = validation.get('issues', [])
            if issues:
                print("   - Top Issues:")
                for issue in issues[:3]:
                    print(f"     • {issue.get('type', 'Unknown')}: {issue.get('message', 'No message')}")
            
            return validation
        else:
            print(f"❌ Validation failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Validation error: {e}")
        return None

def test_quick_import(filename):
    """Test quick import functionality"""
    print(f"\n🔄 Testing quick import...")
    
    try:
        with open(filename, 'rb') as f:
            files = {'file': (filename, f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = requests.post(f"{BASE_URL}/import-data/", files=files)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Quick import completed")
            print(f"   - Session ID: {result.get('session_id', 'N/A')}")
            print(f"   - Records Imported: {result.get('records_imported', 'N/A')}")
            print(f"   - Table: {result.get('table_name', 'N/A')}")
            return result
        else:
            print(f"❌ Import failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Import error: {e}")
        return None

def test_enhanced_preview(session_id):
    """Test enhanced preview functionality"""
    print(f"\n🔄 Testing enhanced preview...")
    
    try:
        # Test basic preview
        response = requests.get(f"{BASE_URL}/preview-enhanced/{session_id}")
        
        if response.status_code == 200:
            preview = response.json()
            print(f"✅ Enhanced preview loaded")
            print(f"   - Total Records: {preview.get('total_records', 'N/A')}")
            print(f"   - Current Page: {preview.get('current_page', 'N/A')}")
            print(f"   - Records per Page: {preview.get('records_per_page', 'N/A')}")
            
            # Test with pagination
            response = requests.get(f"{BASE_URL}/preview-enhanced/{session_id}?page=2&per_page=25")
            if response.status_code == 200:
                print("✅ Pagination working")
            
            # Test with sorting
            response = requests.get(f"{BASE_URL}/preview-enhanced/{session_id}?sort_by=id&sort_order=desc")
            if response.status_code == 200:
                print("✅ Sorting working")
            
            return preview
        else:
            print(f"❌ Preview failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Preview error: {e}")
        return None

def test_export_functionality(session_id):
    """Test export functionality"""
    print(f"\n🔄 Testing export functionality...")
    
    try:
        # Test CSV export
        response = requests.get(f"{BASE_URL}/export-csv/{session_id}")
        
        if response.status_code == 200:
            print(f"✅ CSV export working")
            print(f"   - Content Type: {response.headers.get('content-type', 'N/A')}")
            print(f"   - Content Length: {len(response.content)} bytes")
            
            # Save sample export
            with open(f"test_export_{session_id}.csv", 'wb') as f:
                f.write(response.content)
            print(f"   - Sample saved as: test_export_{session_id}.csv")
            
            return True
        else:
            print(f"❌ Export failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Export error: {e}")
        return False

def test_statistics_endpoint(session_id):
    """Test statistics endpoint"""
    print(f"\n🔄 Testing statistics endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/data-statistics/{session_id}")
        
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ Statistics loaded")
            print(f"   - Columns analyzed: {len(stats.get('columns', {}))}")
            
            # Show sample column stats
            columns = stats.get('columns', {})
            if columns:
                first_col = list(columns.keys())[0]
                col_stats = columns[first_col]
                print(f"   - Sample column '{first_col}':")
                print(f"     • Null count: {col_stats.get('null_count', 'N/A')}")
                print(f"     • Unique values: {col_stats.get('unique_count', 'N/A')}")
                print(f"     • Data type: {col_stats.get('data_type', 'N/A')}")
            
            return stats
        else:
            print(f"❌ Statistics failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Statistics error: {e}")
        return None

def run_comprehensive_test():
    """Run comprehensive test of all features"""
    print("🚀 Starting Comprehensive ETL Feature Test")
    print("=" * 50)
    
    # Test backend health
    if not test_health():
        print("❌ Backend not available. Please start the backend first.")
        return
    
    # Test files to upload
    test_files = [
        'sample_quality_test.xlsx',
        'sample_large_performance.xlsx',
        'sample_mixed_datatypes.xlsx'
    ]
    
    results = {}
    
    for filename in test_files:
        print(f"\n{'='*20} Testing {filename} {'='*20}")
        
        # Upload file
        file_info = test_file_upload(filename)
        if not file_info:
            continue
        
        # Import data
        import_result = test_quick_import(filename)
        if not import_result:
            continue
        
        session_id = import_result.get('session_id')
        if not session_id:
            continue
        
        # Validate data (after import)
        validation = test_data_validation(session_id)
        
        # Test enhanced preview
        preview = test_enhanced_preview(session_id)
        
        # Test statistics
        stats = test_statistics_endpoint(session_id)
        
        # Test export
        export_success = test_export_functionality(session_id)
        
        results[filename] = {
            'upload': file_info is not None,
            'validation': validation is not None,
            'import': import_result is not None,
            'preview': preview is not None,
            'statistics': stats is not None,
            'export': export_success,
            'session_id': session_id
        }
    
    # Summary
    print(f"\n{'='*20} TEST SUMMARY {'='*20}")
    
    total_tests = 0
    passed_tests = 0
    
    for filename, result in results.items():
        print(f"\n📁 {filename}:")
        for test_name, passed in result.items():
            if test_name != 'session_id':
                total_tests += 1
                if passed:
                    passed_tests += 1
                    print(f"   ✅ {test_name.title()}")
                else:
                    print(f"   ❌ {test_name.title()}")
    
    print(f"\n🎯 Overall Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! ETL system is working perfectly!")
    else:
        print(f"⚠️  {total_tests - passed_tests} tests failed. Check the logs above.")
    
    return results

if __name__ == "__main__":
    results = run_comprehensive_test()