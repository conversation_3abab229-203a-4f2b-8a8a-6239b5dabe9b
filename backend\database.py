from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, text
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

# Database configuration
# Lazy initialization to avoid connection issues on import
engine = None
SessionLocal = None

def init_database():
    """Initialize database connection"""
    global engine, SessionLocal
    if engine is None:
        from config import DATABASE_URL
        # Optimized settings for better performance
        engine = create_engine(
            DATABASE_URL, 
            pool_size=5,
            max_overflow=10,
            pool_pre_ping=True,
            pool_recycle=3600,
            echo=False  # Disable SQL logging for performance
        )
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return engine, SessionLocal
Base = declarative_base()

# Generic table for imported data
class ImportedData(Base):
    __tablename__ = "imported_data"
    
    id = Column(Integer, primary_key=True, index=True)
    import_session_id = Column(String(50), index=True)  # To group imports
    row_data = Column(String(2000))  # JSON string of the row data
    created_at = Column(DateTime, default=datetime.utcnow)

# Create tables
def create_tables():
    engine, _ = init_database()
    Base.metadata.create_all(bind=engine)
    
def create_all_tables():
    """Create all tables including new models"""
    from models import TableSchema, FieldDefinition, ImportMapping, MappedData
    engine, _ = init_database()
    Base.metadata.create_all(bind=engine)

# Dependency to get DB session
def get_db():
    _, SessionLocal = init_database()
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()