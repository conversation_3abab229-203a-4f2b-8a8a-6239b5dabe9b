"""
Direct table creation using the working connection
"""

from sqlalchemy import create_engine, Column, Integer, String, DateTime, MetaData, Table
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
from config import DATABASE_URL

print(f"Using DATABASE_URL: {DATABASE_URL}")

try:
    # Create engine
    engine = create_engine(DATABASE_URL)
    
    # Test connection first
    with engine.connect() as connection:
        from sqlalchemy import text
        result = connection.execute(text("SELECT 1"))
        print("✅ Connection test successful!")
    
    # Create base and table
    Base = declarative_base()
    
    class ImportedData(Base):
        __tablename__ = "imported_data"
        
        id = Column(Integer, primary_key=True, index=True)
        import_session_id = Column(String(50), index=True)
        row_data = Column(String(2000))
        created_at = Column(DateTime, default=datetime.utcnow)
    
    # Create all tables
    print("Creating tables...")
    Base.metadata.create_all(bind=engine)
    print("✅ Tables created successfully!")
    
    # Verify tables were created
    with engine.connect() as connection:
        result = connection.execute(text("SHOW TABLES"))
        tables = result.fetchall()
        print(f"Created tables: {[table[0] for table in tables]}")
    
    print("\n🎉 Database setup complete!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()