"""
Verify the sample Excel files were created correctly
"""

import pandas as pd
import os

def verify_file(filename, expected_rows=None):
    """Verify a sample file exists and has correct structure"""
    
    if not os.path.exists(filename):
        print(f"❌ {filename} not found")
        return False
    
    try:
        df = pd.read_excel(filename)
        print(f"✅ {filename}")
        print(f"   Rows: {len(df)}")
        print(f"   Columns: {list(df.columns)}")
        
        if expected_rows and len(df) != expected_rows:
            print(f"   ⚠️  Expected {expected_rows} rows, got {len(df)}")
        
        # Show first row as sample
        if len(df) > 0:
            print(f"   Sample: {dict(df.iloc[0])}")
        
        print()
        return True
        
    except Exception as e:
        print(f"❌ Error reading {filename}: {e}")
        return False

def main():
    """Verify all sample files"""
    
    print("Verifying sample Excel files...\n")
    
    files_to_check = [
        ("sample_employees.xlsx", 5),
        ("sample_customers.xlsx", 5),
        ("sample_mixed_format.xlsx", 5),
        ("sample_large_dataset.xlsx", 100),
        ("sample_problematic_data.xlsx", 5)
    ]
    
    success_count = 0
    
    for filename, expected_rows in files_to_check:
        if verify_file(filename, expected_rows):
            success_count += 1
    
    print(f"Summary: {success_count}/{len(files_to_check)} files verified successfully")
    
    if success_count == len(files_to_check):
        print("\n🎉 All sample files are ready for testing!")
        print("\nNext steps:")
        print("1. Start the backend server: python backend/start_server.py")
        print("2. Start the frontend: npm start (in frontend directory)")
        print("3. Upload any of the sample files to test column mapping")
    else:
        print("\n⚠️  Some files had issues. Check the output above.")

if __name__ == "__main__":
    main()