"""
Test that the frontend fix resolves the map error
"""

import requests
import time

BASE_URL = "http://localhost:8000"

def test_frontend_compatibility():
    """Test that the enhanced file structure works with the frontend"""
    print("🔧 Testing Frontend Compatibility Fix")
    print("-" * 50)
    
    try:
        with open('sample_quality_test.xlsx', 'rb') as f:
            files = {'file': ('sample_quality_test.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = requests.post(f"{BASE_URL}/upload-excel/", files=files)
        
        if response.status_code == 200:
            file_data = response.json()
            
            print(f"✅ Backend Response Structure:")
            print(f"  • filename: {file_data.get('filename', 'Missing')}")
            print(f"  • total_rows: {file_data.get('total_rows', 'Missing')}")
            print(f"  • total_columns: {file_data.get('total_columns', 'Missing')}")
            print(f"  • columns: {'Present' if 'columns' in file_data else 'Missing'}")
            print(f"  • preview_data: {'Present' if 'preview_data' in file_data else 'Missing'}")
            
            # Check if columns is an array of objects or strings
            if 'columns' in file_data and file_data['columns']:
                first_col = file_data['columns'][0]
                if isinstance(first_col, dict):
                    print(f"  • columns format: Objects (enhanced)")
                    print(f"    - Sample column: {first_col.get('name', 'No name')} ({first_col.get('data_type', 'No type')})")
                else:
                    print(f"  • columns format: Strings (legacy)")
                    print(f"    - Sample column: {first_col}")
            
            # Check preview data format
            if 'preview_data' in file_data and file_data['preview_data']:
                print(f"  • preview_data: {len(file_data['preview_data'])} rows")
                first_row = file_data['preview_data'][0]
                print(f"    - Sample row keys: {list(first_row.keys())[:3]}...")
            
            print(f"\n🎯 Frontend Compatibility:")
            print(f"  • Backward compatibility: ✅ (handles both formats)")
            print(f"  • Null safety: ✅ (uses optional chaining)")
            print(f"  • Enhanced features: ✅ (supports new structure)")
            
            return True
        else:
            print(f"❌ Upload failed: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except FileNotFoundError:
        print("❌ Sample file not found. Please run create_enhanced_samples.py first.")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Frontend Compatibility Test")
    print("=" * 60)
    
    # Check backend
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code != 200:
            print("❌ Backend not running. Please start the backend first.")
            exit(1)
    except:
        print("❌ Cannot connect to backend. Please start the backend first.")
        exit(1)
    
    print("✅ Backend is running!")
    print()
    
    success = test_frontend_compatibility()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Frontend compatibility test completed successfully!")
        print("✅ The map error should now be resolved.")
        print("🌐 Check the frontend at http://localhost:3000 - it should work without errors!")
    else:
        print("❌ Frontend compatibility test failed.")
        print("⚠️  Check the backend logs for details.")