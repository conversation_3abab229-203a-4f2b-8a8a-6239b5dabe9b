#!/usr/bin/env python3
"""
Test frontend integration by checking if React app is running
and can communicate with backend
"""

import requests
import time

def test_frontend_running():
    """Test if React frontend is running"""
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is running on http://localhost:3000")
            return True
        else:
            print(f"❌ Frontend returned status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Frontend is not running on http://localhost:3000")
        return False
    except Exception as e:
        print(f"❌ Error checking frontend: {e}")
        return False

def test_backend_cors():
    """Test if backend CORS is properly configured"""
    try:
        # Simulate a preflight request
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        response = requests.options("http://localhost:8000/upload-excel/", headers=headers)
        
        if response.status_code in [200, 204]:
            print("✅ CORS is properly configured")
            return True
        else:
            print(f"❌ CORS preflight failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error testing CORS: {e}")
        return False

def test_api_endpoints():
    """Test key API endpoints that frontend uses"""
    base_url = "http://localhost:8000"
    
    endpoints = [
        ("/health", "GET"),
        ("/api/pipeline-templates", "GET"),
        ("/stats", "GET"),
        ("/schemas/", "GET"),
        ("/tables/", "GET")
    ]
    
    results = {}
    
    for endpoint, method in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}")
            
            if response.status_code == 200:
                print(f"✅ {endpoint} - Working")
                results[endpoint] = True
            else:
                print(f"❌ {endpoint} - Status: {response.status_code}")
                results[endpoint] = False
        except Exception as e:
            print(f"❌ {endpoint} - Error: {e}")
            results[endpoint] = False
    
    return results

def main():
    """Run frontend integration tests"""
    print("🔄 Testing Frontend Integration")
    print("=" * 40)
    
    # Test frontend
    frontend_ok = test_frontend_running()
    
    # Test CORS
    cors_ok = test_backend_cors()
    
    # Test API endpoints
    print("\n🔄 Testing API Endpoints:")
    api_results = test_api_endpoints()
    
    # Summary
    print(f"\n{'='*20} INTEGRATION SUMMARY {'='*20}")
    print(f"Frontend Running: {'✅' if frontend_ok else '❌'}")
    print(f"CORS Configured: {'✅' if cors_ok else '❌'}")
    
    api_working = sum(api_results.values())
    api_total = len(api_results)
    print(f"API Endpoints: {api_working}/{api_total} working")
    
    if frontend_ok and cors_ok and api_working == api_total:
        print("\n🎉 Frontend integration is working perfectly!")
        print("\n🚀 Ready for testing:")
        print("1. Open http://localhost:3000 in your browser")
        print("2. Upload one of the sample Excel files")
        print("3. Test all the enhanced features:")
        print("   - Enhanced Preview with pagination and sorting")
        print("   - Data Quality Validator")
        print("   - Export functionality")
        print("   - Pipeline Templates")
        print("   - Keyboard shortcuts (Ctrl+R, Ctrl+E, etc.)")
    else:
        print("\n⚠️  Some integration issues found:")
        if not frontend_ok:
            print("- Start the frontend: cd frontend && npm start")
        if not cors_ok:
            print("- Check backend CORS configuration")
        if api_working < api_total:
            print("- Some API endpoints are not working")

if __name__ == "__main__":
    main()