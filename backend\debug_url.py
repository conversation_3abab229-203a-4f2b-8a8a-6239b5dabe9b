"""
Debug the DATABASE_URL generation
"""

from config import DATABASE_URL, DB_CONFIG

print("Database Configuration:")
print(f"Host: {DB_CONFIG['host']}")
print(f"Port: {DB_CONFIG['port']}")
print(f"User: {DB_CONFIG['user']}")
print(f"Password: {DB_CONFIG['password']}")
print(f"Database: {DB_CONFIG['database']}")

print(f"\nGenerated DATABASE_URL:")
print(DATABASE_URL)

# Test SQLAlchemy connection
try:
    from sqlalchemy import create_engine
    engine = create_engine(DATABASE_URL)
    
    # Test connection
    with engine.connect() as connection:
        from sqlalchemy import text
        result = connection.execute(text("SELECT 1"))
        print("\n✅ SQLAlchemy connection successful!")
        
except Exception as e:
    print(f"\n❌ SQLAlchemy connection failed: {e}")
    
    # Try with manual URL encoding
    from urllib.parse import quote_plus
    manual_url = f"mysql+pymysql://{DB_CONFIG['user']}:{quote_plus(DB_CONFIG['password'])}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"
    print(f"\nTrying manual URL: {manual_url}")
    
    try:
        engine2 = create_engine(manual_url)
        with engine2.connect() as connection:
            from sqlalchemy import text
            result = connection.execute(text("SELECT 1"))
            print("✅ Manual URL connection successful!")
    except Exception as e2:
        print(f"❌ Manual URL also failed: {e2}")