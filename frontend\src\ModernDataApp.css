/* Modern Data App Styles */
.modern-app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Header */
.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo {
  font-size: 2rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-section h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
}

.tagline {
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.header-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.header-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.header-btn.secondary {
  background: rgba(255, 255, 255, 0.8);
  color: #4a5568;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.header-btn.secondary:hover {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Main Content */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: calc(100vh - 100px);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Upload Section */
.upload-section {
  width: 100%;
  max-width: 600px;
}

.upload-zone {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 2px dashed #cbd5e0;
  border-radius: 1rem;
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-zone:hover,
.upload-zone.drag-active {
  border-color: #667eea;
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.upload-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

.upload-zone h2 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-weight: 600;
  font-size: 1.5rem;
}

.upload-zone p {
  margin: 0 0 1.5rem 0;
  color: #718096;
  font-size: 1rem;
}

.supported-formats {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.supported-formats span {
  background: #edf2f7;
  color: #4a5568;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Processing Section */
.processing-section {
  width: 100%;
  max-width: 800px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* File Card */
.file-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.file-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.file-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.file-details h3 {
  margin: 0 0 0.25rem 0;
  color: #2d3748;
  font-weight: 600;
  font-size: 1.125rem;
}

.file-details p {
  margin: 0;
  color: #718096;
  font-size: 0.875rem;
}

.remove-btn {
  margin-left: auto;
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #a0aec0;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background: #fed7d7;
  color: #e53e3e;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state p {
  margin: 0;
  color: #718096;
  font-weight: 500;
}

/* Error State */
.error-state {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 0.5rem;
  color: #c53030;
}

.error-icon {
  font-size: 1.25rem;
}

.error-state p {
  margin: 0;
  font-weight: 500;
}

/* File Analysis */
.file-analysis {
  border-top: 1px solid #e2e8f0;
  padding-top: 1.5rem;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 0.5rem;
}

.stat-label {
  display: block;
  font-size: 0.75rem;
  color: #718096;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.25rem;
}

.stat-value {
  display: block;
  font-size: 1.25rem;
  color: #2d3748;
  font-weight: 700;
}

/* Columns Preview */
.columns-preview h4 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-weight: 600;
  font-size: 1rem;
}

.columns-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.column-tag {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.column-tag.more {
  background: #e2e8f0;
  color: #4a5568;
}

/* Data Preview Section */
.data-preview-section {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.data-preview-section h4 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-weight: 600;
  font-size: 1rem;
}

.preview-table-wrapper {
  background: #f7fafc;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
}

.modern-preview-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.modern-preview-table th {
  background: #edf2f7;
  padding: 0.75rem 0.5rem;
  text-align: left;
  font-weight: 600;
  color: #2d3748;
  border-bottom: 2px solid #e2e8f0;
}

.modern-preview-table td {
  padding: 0.75rem 0.5rem;
  border-bottom: 1px solid #e2e8f0;
  color: #4a5568;
}

.modern-preview-table tr:hover {
  background: rgba(102, 126, 234, 0.05);
}

.preview-note {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #718096;
  text-align: center;
  font-style: italic;
}

/* Action Section */
.action-section {
  display: flex;
  justify-content: center;
}

.import-options {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  width: 100%;
  max-width: 500px;
}

.advanced-options {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  justify-content: center;
}

.option-btn {
  padding: 0.5rem 1rem;
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 0.5rem;
  background: rgba(255, 255, 255, 0.8);
  color: #4a5568;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.option-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.5);
  transform: translateY(-1px);
}

.action-btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 200px;
  justify-content: center;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.action-btn.primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.action-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
}

.btn-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Success Section */
.success-section {
  display: flex;
  justify-content: center;
}

.success-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.success-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.success-card h3 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-weight: 700;
  font-size: 1.5rem;
}

.success-card p {
  margin: 0 0 1.5rem 0;
  color: #718096;
  font-size: 1rem;
}

.success-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.success-stat {
  text-align: center;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 0.5rem;
  min-width: 120px;
}

.success-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  flex-wrap: wrap;
}

.success-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.success-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.success-btn.secondary {
  background: rgba(255, 255, 255, 0.8);
  color: #4a5568;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.success-btn:hover {
  transform: translateY(-1px);
}

.success-btn.primary:hover {
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.success-btn.secondary:hover {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.success-btn.outline {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.success-btn.outline:hover {
  background: #667eea;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .analysis-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .success-actions {
    flex-direction: column;
  }
  
  .success-stats {
    flex-direction: column;
  }
}