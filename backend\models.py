"""
Database models for column mapping system
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from database import Base

class TableSchema(Base):
    """Define custom table schemas for imports"""
    __tablename__ = "table_schemas"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, index=True)  # e.g., "employees", "customers"
    description = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # Relationship to field definitions
    fields = relationship("FieldDefinition", back_populates="schema")

class FieldDefinition(Base):
    """Define fields for custom schemas"""
    __tablename__ = "field_definitions"
    
    id = Column(Integer, primary_key=True, index=True)
    schema_id = Column(Integer, ForeignKey("table_schemas.id"))
    field_name = Column(String(100))  # Database field name
    display_name = Column(String(100))  # Human-readable name
    field_type = Column(String(50))  # text, number, email, date, etc.
    is_required = Column(Boolean, default=False)
    default_value = Column(String(200))
    validation_rules = Column(Text)  # JSON string with validation rules
    order_index = Column(Integer, default=0)
    
    # Relationship back to schema
    schema = relationship("TableSchema", back_populates="fields")

class ImportMapping(Base):
    """Store column mappings for imports"""
    __tablename__ = "import_mappings"
    
    id = Column(Integer, primary_key=True, index=True)
    import_session_id = Column(String(50), index=True)
    excel_columns = Column(Text)  # JSON string of all Excel columns
    column_mappings = Column(Text)  # JSON string of column mappings
    transformation_rules = Column(Text)  # JSON string of transformation rules
    created_at = Column(DateTime, default=datetime.utcnow)

class MappedData(Base):
    """Store imported data with proper field mapping"""
    __tablename__ = "mapped_data"
    
    id = Column(Integer, primary_key=True, index=True)
    import_session_id = Column(String(50), index=True)
    schema_id = Column(Integer, ForeignKey("table_schemas.id"))
    row_data = Column(Text)  # JSON string of mapped data
    validation_errors = Column(Text)  # JSON string of any validation errors
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationship to schema
    schema = relationship("TableSchema")