/* Chart Container Styles */
.chart-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
}

.chart-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
}

.chart-title-section {
  flex: 1;
}

.chart-title {
  margin: 0 0 0.25rem 0;
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
}

.chart-subtitle {
  margin: 0;
  font-size: 0.875rem;
  color: #64748b;
}

.chart-actions {
  display: flex;
  gap: 0.5rem;
}

.chart-content {
  padding: 1.5rem;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #64748b;
}

.chart-loading p {
  margin: 0;
  font-size: 0.875rem;
}

.chart-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #dc2626;
  text-align: center;
}

.error-icon {
  font-size: 2rem;
}

.chart-error p {
  margin: 0;
  font-size: 0.875rem;
}