"""
Test the optimized validation endpoint
"""

import requests
import time

BASE_URL = "http://localhost:8000"

def test_fast_validation():
    """Test the optimized validation endpoint"""
    print("🧪 Testing Fast Data Validation")
    print("-" * 40)
    
    # First, import some data
    try:
        with open('sample_quality_test.xlsx', 'rb') as f:
            files = {'file': ('sample_quality_test.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = requests.post(f"{BASE_URL}/import-data/", files=files)
        
        if response.status_code == 200:
            import_result = response.json()
            session_id = import_result['session_id']
            print(f"✅ Data imported: {import_result['imported_rows']} rows")
            print(f"🆔 Session ID: {session_id[:8]}...")
            
            # Now test validation speed
            print(f"\n🔍 Running validation analysis...")
            start_time = time.time()
            
            validation_response = requests.get(f"{BASE_URL}/api/validate/{session_id}")
            
            end_time = time.time()
            analysis_time = end_time - start_time
            
            if validation_response.status_code == 200:
                quality_data = validation_response.json()
                
                print(f"⚡ Analysis completed in {analysis_time:.2f} seconds")
                print(f"📊 Quality Score: {quality_data['quality_score']}/100")
                print(f"📈 Records Analyzed: {quality_data['analyzed_records']}/{quality_data['total_records']}")
                print(f"🚨 Issues Found: {len(quality_data['issues'])}")
                
                if quality_data['issues']:
                    print(f"\n📋 Issues:")
                    for issue in quality_data['issues'][:3]:  # Show first 3 issues
                        severity_icon = "🔴" if issue['severity'] == 'high' else "🟡" if issue['severity'] == 'medium' else "🟢"
                        print(f"   {severity_icon} {issue['description']}")
                
                print(f"\n💡 Recommendations:")
                for rec in quality_data['recommendations']:
                    print(f"   • {rec}")
                
                print(f"\n📝 {quality_data['analysis_note']}")
                
                # Performance assessment
                if analysis_time < 1.0:
                    print(f"\n🎉 EXCELLENT: Analysis completed in under 1 second!")
                elif analysis_time < 3.0:
                    print(f"\n✅ GOOD: Analysis completed in under 3 seconds")
                else:
                    print(f"\n⚠️  SLOW: Analysis took {analysis_time:.2f} seconds")
                
                return True
            else:
                print(f"❌ Validation failed: {validation_response.status_code}")
                print(f"Error: {validation_response.text}")
                return False
        else:
            print(f"❌ Import failed: {response.status_code}")
            return False
            
    except FileNotFoundError:
        print("❌ Sample file not found. Please run create_enhanced_samples.py first.")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Fast Validation Performance Test")
    print("=" * 50)
    
    # Check backend
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code != 200:
            print("❌ Backend not running. Please start the backend first.")
            exit(1)
    except:
        print("❌ Cannot connect to backend. Please start the backend first.")
        exit(1)
    
    print("✅ Backend is running!")
    print()
    
    success = test_fast_validation()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Fast validation test completed successfully!")
        print("✅ The optimization is working - validation should now be much faster.")
    else:
        print("❌ Fast validation test failed.")
        print("⚠️  Check the backend logs for details.")