/* Modern App Styles */
.modern-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #2d3748;
}

/* Header */
.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1.25rem;
}

.logo-icon {
  font-size: 1.5rem;
}

.logo-text {
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-right {
  display: flex;
  gap: 0.5rem;
}

.header-button {
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.25rem;
  transition: all 0.2s ease;
}

.header-button:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

/* Step Indicator */
.step-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50px;
  backdrop-filter: blur(10px);
}

.step {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  transition: all 0.3s ease;
  position: relative;
}

.step.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.step.completed {
  background: #48bb78;
  color: white;
}

.step-icon {
  font-size: 1.25rem;
}

.step-content {
  display: flex;
  flex-direction: column;
}

.step-title {
  font-weight: 600;
  font-size: 0.875rem;
}

.step-description {
  font-size: 0.75rem;
  opacity: 0.8;
}

.step-connector {
  width: 2rem;
  height: 2px;
  background: #e2e8f0;
  margin: 0 0.5rem;
}

/* App Body */
.app-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Sidebar */
.sidebar {
  width: 300px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1.5rem;
  overflow-y: auto;
}

.sidebar-section {
  margin-bottom: 2rem;
}

.sidebar-section h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #4a5568;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.2s ease;
  cursor: pointer;
}

.history-item:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateX(4px);
}

.history-icon {
  font-size: 1.25rem;
}

.history-content {
  flex: 1;
}

.history-title {
  font-weight: 500;
  font-size: 0.875rem;
}

.history-subtitle {
  font-size: 0.75rem;
  color: #718096;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.quick-action {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: none;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  text-align: left;
}

.quick-action:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateX(4px);
}

.empty-text {
  color: #a0aec0;
  font-size: 0.875rem;
  text-align: center;
  padding: 1rem;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

/* Error Banner */
.error-banner {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: #fed7d7;
  color: #c53030;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  border-left: 4px solid #e53e3e;
}

.error-icon {
  font-size: 1.25rem;
}

.error-message {
  flex: 1;
  font-weight: 500;
}

.error-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #c53030;
}

/* Step Content */
.step-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Upload Step */
.upload-area {
  text-align: center;
}

.file-drop-zone {
  border: 2px dashed #cbd5e0;
  border-radius: 16px;
  padding: 3rem;
  transition: all 0.3s ease;
  background: rgba(247, 250, 252, 0.5);
  position: relative;
  overflow: hidden;
}

.file-drop-zone:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.file-drop-zone.has-file {
  border-color: #48bb78;
  background: rgba(72, 187, 120, 0.05);
}

.file-input {
  position: absolute;
  inset: 0;
  opacity: 0;
  cursor: pointer;
}

.drop-zone-content {
  display: block;
  cursor: pointer;
}

.drop-zone-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.drop-icon {
  font-size: 4rem;
  opacity: 0.6;
}

.drop-zone-empty h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.drop-zone-empty p {
  color: #718096;
  margin: 0;
}

.supported-formats {
  font-size: 0.875rem;
  color: #a0aec0;
  margin-top: 0.5rem;
}

.file-selected {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  text-align: left;
}

.file-icon {
  font-size: 3rem;
}

.file-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.file-info p {
  margin: 0;
  color: #718096;
}

.loading-bar {
  width: 200px;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  animation: loading 2s infinite;
}

@keyframes loading {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.processing-text {
  color: #667eea;
  font-weight: 500;
  margin-top: 0.5rem;
}

.upload-actions {
  margin-top: 2rem;
}

/* Configure Step */
.config-tabs {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tab-nav {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e2e8f0;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  background: none;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  color: #718096;
}

.tab-button:hover {
  background: rgba(102, 126, 234, 0.05);
  color: #4a5568;
}

.tab-button.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.tab-content {
  flex: 1;
}

.tab-panel {
  height: 100%;
}

/* Quick Import Tab */
.quick-import-panel {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.panel-header h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.panel-header p {
  margin: 0;
  color: #718096;
}

.file-summary {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
}

.summary-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.summary-icon {
  font-size: 2rem;
}

.summary-content h4 {
  margin: 0 0 1rem 0;
  font-weight: 600;
}

.summary-stats {
  display: flex;
  gap: 1rem;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
}

.stat-label {
  font-size: 0.75rem;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.data-preview-card {
  padding: 1.5rem;
  background: rgba(247, 250, 252, 0.8);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.data-preview-card h4 {
  margin: 0 0 1rem 0;
  font-weight: 600;
}

.preview-table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.preview-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.preview-table th {
  background: #f7fafc;
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.875rem;
  border-bottom: 1px solid #e2e8f0;
}

.preview-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #f1f5f9;
  font-size: 0.875rem;
}

.preview-table tr:hover {
  background: #f8fafc;
}

/* Process Step */
.processing-panel {
  text-align: center;
  padding: 4rem 2rem;
}

.processing-animation {
  margin-bottom: 2rem;
}

.spinner-large {
  width: 60px;
  height: 60px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-panel h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.processing-panel p {
  color: #718096;
  margin: 0 0 2rem 0;
}

.progress-bar {
  width: 300px;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin: 0 auto;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  animation: progress 2s infinite;
}

@keyframes progress {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Results Step */
.results-panel {
  text-align: center;
}

.success-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
}

.success-icon {
  font-size: 4rem;
}

.success-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.success-content p {
  margin: 0;
  color: #718096;
  font-size: 1.125rem;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.result-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: rgba(102, 126, 234, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}

.result-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

.result-icon {
  font-size: 2rem;
}

.result-content {
  text-align: left;
}

.result-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
}

.result-label {
  font-size: 0.875rem;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.result-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.warnings-panel {
  background: #fef5e7;
  border: 1px solid #f6ad55;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: left;
}

.warnings-panel h4 {
  margin: 0 0 1rem 0;
  color: #c05621;
}

.warnings-panel ul {
  margin: 0;
  padding-left: 1.5rem;
}

.warnings-panel li {
  color: #744210;
  margin-bottom: 0.5rem;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  font-size: 0.875rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
}

.btn-secondary {
  background: #4a5568;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #2d3748;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-outline:hover:not(:disabled) {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #a0aec0;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header {
    padding: 1rem;
  }
  
  .header-center {
    display: none;
  }
  
  .sidebar {
    display: none;
  }
  
  .main-content {
    padding: 1rem;
  }
  
  .step-content {
    padding: 1rem;
  }
  
  .file-summary {
    grid-template-columns: 1fr;
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  .result-actions {
    flex-direction: column;
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.step-content {
  animation: fadeIn 0.5s ease-out;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}