"""
Test MySQL connection with current config
"""

import pymysql
from config import DB_CONFIG

def test_mysql_connection():
    """Test connection with current config"""
    
    print("Testing MySQL connection...")
    print(f"Host: {DB_CONFIG['host']}")
    print(f"Port: {DB_CONFIG['port']}")
    print(f"User: {DB_CONFIG['user']}")
    print(f"Password: {'*' * len(DB_CONFIG['password'])}")
    
    try:
        # Test connection without database
        print("\n1. Testing connection to MySQL server...")
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            charset='utf8mb4'
        )
        print("✅ Connected to MySQL server successfully!")
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"MySQL Version: {version[0]}")
        
        connection.close()
        
        # Test connection with database
        print(f"\n2. Testing connection to '{DB_CONFIG['database']}' database...")
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database'],
            charset='utf8mb4'
        )
        print("✅ Connected to excel_importer database successfully!")
        
        with connection.cursor() as cursor:
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"Existing tables: {[table[0] for table in tables]}")
        
        connection.close()
        
        print("\n🎉 All connections successful!")
        
    except pymysql.Error as e:
        print(f"❌ MySQL Error: {e}")
        
        if "Access denied" in str(e):
            print("\nTroubleshooting Access Denied:")
            print("1. Verify your MySQL password is correct")
            print("2. Try connecting with MySQL Workbench or command line:")
            print(f"   mysql -u {DB_CONFIG['user']} -p -h {DB_CONFIG['host']}")
            print("3. Check if the user has proper privileges:")
            print("   GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost';")
            
        elif "Unknown database" in str(e):
            print(f"\nThe database '{DB_CONFIG['database']}' doesn't exist yet.")
            print("This is normal for first-time setup.")
            
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    test_mysql_connection()