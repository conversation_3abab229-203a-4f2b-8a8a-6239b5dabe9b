"""
Simple database setup script
Edit config.py first to set your MySQL credentials
"""

import pymysql
from config import DB_CONFIG
from database import create_tables

def setup_database():
    """Create database and tables"""
    
    print("Setting up Excel Importer Database...")
    print(f"Host: {DB_CONFIG['host']}")
    print(f"User: {DB_CONFIG['user']}")
    print(f"Database: {DB_CONFIG['database']}")
    
    try:
        # Connect to MySQL server (without database)
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # Create database
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DB_CONFIG['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"✅ Database '{DB_CONFIG['database']}' created successfully!")
        
        connection.close()
        
        # Create tables
        print("Creating tables...")
        create_tables()
        print("✅ Tables created successfully!")
        
        print("\n🎉 Database setup complete!")
        print("You can now start the FastAPI server with: uvicorn main:app --reload")
        
    except pymysql.Error as e:
        print(f"❌ Database error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure MySQL server is running")
        print("2. Check your credentials in config.py")
        print("3. Ensure the user has CREATE DATABASE privileges")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    setup_database()