"""
Create the new tables for column mapping
"""

from database import create_all_tables
from models import TableSchema, FieldDefinition
from database import init_database

def setup_mapping_tables():
    """Create mapping tables and add default schemas"""
    
    print("Creating column mapping tables...")
    
    try:
        # Create all tables
        create_all_tables()
        print("✅ Tables created successfully")
        
        # Add default schemas
        engine, SessionLocal = init_database()
        db = SessionLocal()
        
        # Check if default schema exists
        existing_schema = db.query(TableSchema).filter(TableSchema.name == "employees").first()
        
        if not existing_schema:
            print("Creating default 'employees' schema...")
            
            # Create employees schema
            employees_schema = TableSchema(
                name="employees",
                description="Standard employee data schema"
            )
            db.add(employees_schema)
            db.flush()
            
            # Add employee fields
            employee_fields = [
                {"field_name": "name", "display_name": "Full Name", "field_type": "text", "is_required": True, "order_index": 1},
                {"field_name": "email", "display_name": "Email Address", "field_type": "email", "is_required": True, "order_index": 2},
                {"field_name": "age", "display_name": "Age", "field_type": "number", "is_required": False, "order_index": 3},
                {"field_name": "department", "display_name": "Department", "field_type": "text", "is_required": False, "order_index": 4},
                {"field_name": "salary", "display_name": "Salary", "field_type": "number", "is_required": False, "order_index": 5},
                {"field_name": "hire_date", "display_name": "Hire Date", "field_type": "date", "is_required": False, "order_index": 6},
                {"field_name": "phone", "display_name": "Phone Number", "field_type": "text", "is_required": False, "order_index": 7}
            ]
            
            for field_data in employee_fields:
                field = FieldDefinition(
                    schema_id=employees_schema.id,
                    **field_data
                )
                db.add(field)
            
            db.commit()
            print("✅ Default 'employees' schema created")
        
        # Create customers schema
        existing_customers = db.query(TableSchema).filter(TableSchema.name == "customers").first()
        
        if not existing_customers:
            print("Creating default 'customers' schema...")
            
            customers_schema = TableSchema(
                name="customers",
                description="Customer data schema"
            )
            db.add(customers_schema)
            db.flush()
            
            customer_fields = [
                {"field_name": "company_name", "display_name": "Company Name", "field_type": "text", "is_required": True, "order_index": 1},
                {"field_name": "contact_name", "display_name": "Contact Name", "field_type": "text", "is_required": False, "order_index": 2},
                {"field_name": "email", "display_name": "Email", "field_type": "email", "is_required": True, "order_index": 3},
                {"field_name": "phone", "display_name": "Phone", "field_type": "text", "is_required": False, "order_index": 4},
                {"field_name": "address", "display_name": "Address", "field_type": "text", "is_required": False, "order_index": 5},
                {"field_name": "city", "display_name": "City", "field_type": "text", "is_required": False, "order_index": 6},
                {"field_name": "country", "display_name": "Country", "field_type": "text", "is_required": False, "order_index": 7}
            ]
            
            for field_data in customer_fields:
                field = FieldDefinition(
                    schema_id=customers_schema.id,
                    **field_data
                )
                db.add(field)
            
            db.commit()
            print("✅ Default 'customers' schema created")
        
        db.close()
        print("\n🎉 Column mapping setup complete!")
        
    except Exception as e:
        print(f"❌ Error setting up mapping tables: {e}")

if __name__ == "__main__":
    setup_mapping_tables()