"""
Database configuration
"""

import os

# Database configuration - modify these values for your setup
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'root@123',  # Set your MySQL password here
    'database': 'excel_importer'
}

# Build DATABASE_URL from config
def get_database_url():
    config = DB_CONFIG.copy()
    
    # Override with environment variables if they exist
    config['host'] = os.getenv('DB_HOST', config['host'])
    config['port'] = int(os.getenv('DB_PORT', config['port']))
    config['user'] = os.getenv('DB_USER', config['user'])
    config['password'] = os.getenv('DB_PASSWORD', config['password'])
    config['database'] = os.getenv('DB_NAME', config['database'])
    
    if config['password']:
        # URL encode the password to handle special characters
        from urllib.parse import quote_plus
        encoded_password = quote_plus(config['password'])
        return f"mysql+pymysql://{config['user']}:{encoded_password}@{config['host']}:{config['port']}/{config['database']}"
    else:
        return f"mysql+pymysql://{config['user']}@{config['host']}:{config['port']}/{config['database']}"

DATABASE_URL = get_database_url()