"""
Verify the imported data
"""

import pymysql
import json
from config import DB_CONFIG

def verify_imported_data():
    """Check what data was imported"""
    
    try:
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database'],
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # Get total count
            cursor.execute("SELECT COUNT(*) FROM imported_data")
            total_count = cursor.fetchone()[0]
            print(f"✅ Total imported records: {total_count}")
            
            # Get unique sessions
            cursor.execute("SELECT DISTINCT import_session_id FROM imported_data")
            sessions = cursor.fetchall()
            print(f"✅ Import sessions: {len(sessions)}")
            
            # Show sample data
            cursor.execute("SELECT * FROM imported_data LIMIT 3")
            records = cursor.fetchall()
            
            print("\n📋 Sample imported data:")
            for record in records:
                id, session_id, row_data, created_at = record
                data = json.loads(row_data)
                print(f"  ID: {id}")
                print(f"  Session: {session_id[:8]}...")
                print(f"  Data: {data}")
                print(f"  Created: {created_at}")
                print("  ---")
        
        connection.close()
        print("\n🎉 Data verification complete!")
        
    except Exception as e:
        print(f"❌ Error verifying data: {e}")

if __name__ == "__main__":
    verify_imported_data()