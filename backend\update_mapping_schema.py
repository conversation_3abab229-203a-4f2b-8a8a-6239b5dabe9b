"""
Update the import_mappings table schema to handle larger data
"""

from database import init_database
import pymysql
from config import DB_CONFIG

def update_mapping_schema():
    """Update the import_mappings table schema"""
    
    print("Updating import_mappings table schema...")
    
    try:
        # Connect directly to MySQL
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database'],
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # Check if table exists
            cursor.execute("SHOW TABLES LIKE 'import_mappings'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                print("Dropping existing import_mappings table...")
                cursor.execute("DROP TABLE import_mappings")
                print("✅ Old table dropped")
            
            # Create new table with updated schema
            create_table_sql = """
            CREATE TABLE import_mappings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                import_session_id VARCHAR(50) NOT NULL,
                excel_columns TEXT,
                column_mappings TEXT,
                transformation_rules TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_session_id (import_session_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            cursor.execute(create_table_sql)
            print("✅ New import_mappings table created with TEXT columns")
        
        connection.commit()
        connection.close()
        
        print("\n🎉 Schema update completed successfully!")
        
    except Exception as e:
        print(f"❌ Error updating schema: {e}")

def verify_schema():
    """Verify the updated schema"""
    
    try:
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database'],
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            cursor.execute("DESCRIBE import_mappings")
            columns = cursor.fetchall()
            
            print("\nUpdated table structure:")
            for column in columns:
                print(f"  {column[0]}: {column[1]}")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ Error verifying schema: {e}")

if __name__ == "__main__":
    update_mapping_schema()
    verify_schema()