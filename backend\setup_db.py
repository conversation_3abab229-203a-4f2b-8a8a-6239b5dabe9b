"""
Database setup script for Excel Importer
Run this script to create the MySQL database and tables
"""

import pymysql
from database import create_tables, DATABASE_URL
import os

def create_database():
    """Create the database if it doesn't exist"""
    try:
        # Default values for local development
        user = 'root'
        password = input("Enter MySQL root password (or press Enter for no password): ").strip()
        if not password:
            password = ''
        
        host = 'localhost'
        port = 3306
        database = 'excel_importer'
        
        print(f"Attempting to create database: {database}")
        print(f"Host: {host}, Port: {port}, User: {user}")
        
        # Connect to MySQL server (without specifying database)
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # Create database if it doesn't exist
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"Database '{database}' created successfully!")
        
        connection.close()
        
        # Update the DATABASE_URL environment variable for this session
        global DATABASE_URL
        if password:
            DATABASE_URL = f"mysql+pymysql://{user}:{password}@{host}:{port}/{database}"
        else:
            DATABASE_URL = f"mysql+pymysql://{user}@{host}:{port}/{database}"
        
        # Update the database module
        import database
        database.DATABASE_URL = DATABASE_URL
        database.engine = database.create_engine(DATABASE_URL)
        database.SessionLocal = database.sessionmaker(autocommit=False, autoflush=False, bind=database.engine)
        
        # Now create the tables
        print("Creating tables...")
        create_tables()
        print("Tables created successfully!")
        print(f"\nTo use this database, set the environment variable:")
        print(f"set DATABASE_URL={DATABASE_URL}")
        
    except Exception as e:
        print(f"Error setting up database: {e}")
        print("\nPlease ensure:")
        print("1. MySQL server is running")
        print("2. Username/password are correct")
        print("3. User has permission to create databases")
        print("4. Try connecting with MySQL Workbench or command line first to verify credentials")

if __name__ == "__main__":
    create_database()