{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@reactflow/background": "^11.3.14", "@reactflow/controls": "^11.2.14", "@reactflow/core": "^11.11.4", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "chart.js": "^4.5.0", "framer-motion": "^12.23.12", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-scripts": "5.0.1", "socket.io-client": "^4.8.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}