# Sample Excel Files for Column Mapping

This directory contains various Excel files to test the column mapping functionality:

## Sample Files

### 1. sample_employees.xlsx
**Purpose**: Standard employee data with common column variations
**Columns**: Full Name, Email Address, Years, Dept, Annual Salary, Start Date, Contact Number
**Use Case**: Test auto-mapping with slightly different column names

### 2. sample_customers.xlsx
**Purpose**: Customer data for testing customer schema mapping
**Columns**: Company, Contact Person, Business Email, Phone Number, Street Address, City Name, Country Code
**Use Case**: Test mapping to customer schema

### 3. sample_mixed_format.xlsx
**Purpose**: Employee data with underscore naming convention
**Columns**: employee_name, work_email, age_years, department_name, yearly_compensation, hire_date, mobile_phone
**Use Case**: Test auto-mapping with different naming conventions

### 4. sample_large_dataset.xlsx
**Purpose**: Performance testing with 100 employee records
**Columns**: Employee Name, Work Email, Age, Department, Salary, Hire Date, Phone
**Use Case**: Test import performance and batch processing

### 5. sample_problematic_data.xlsx
**Purpose**: Data with quality issues for validation testing
**Columns**: Name, Email, Age, Department, Salary, Phone
**Issues**: Empty values, invalid emails, non-numeric ages, invalid salaries
**Use Case**: Test data validation and error handling

## Testing Scenarios

### Auto-Mapping Test:
1. Upload sample_employees.xlsx
2. Use "Import with Column Mapping"
3. Select "employees" schema
4. Verify auto-suggestions match correctly

### Manual Mapping Test:
1. Upload sample_mixed_format.xlsx
2. Manually map underscore columns to schema fields
3. Test different field types (text, number, email, date)

### Validation Test:
1. Upload sample_problematic_data.xlsx
2. Try to map and import
3. Verify validation errors are caught and reported

### Performance Test:
1. Upload sample_large_dataset.xlsx
2. Test import speed with 100 records
3. Monitor processing time and memory usage

## Expected Mappings

### Employee Schema Mappings:
- Full Name -> name
- Email Address -> email
- Years/Age -> age
- Dept/Department -> department
- Annual Salary/Salary -> salary
- Start Date/Hire Date -> hire_date
- Contact Number/Phone -> phone

### Customer Schema Mappings:
- Company -> company_name
- Contact Person -> contact_name
- Business Email -> email
- Phone Number -> phone
- Street Address -> address
- City Name -> city
- Country Code -> country