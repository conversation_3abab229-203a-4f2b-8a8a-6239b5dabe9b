"""
Simple script to test database connection
"""

import pymysql
import os

def test_connection():
    """Test MySQL connection with user input"""
    
    print("Testing MySQL Connection...")
    
    host = input("MySQL Host (default: localhost): ").strip() or 'localhost'
    port = int(input("MySQL Port (default: 3306): ").strip() or '3306')
    user = input("MySQL User (default: root): ").strip() or 'root'
    password = input("MySQL Password: ").strip()
    
    try:
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ Connection successful!")
            print(f"MySQL Version: {version[0]}")
            
            # Show databases
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            print(f"Available databases: {[db[0] for db in databases]}")
        
        connection.close()
        
        # Test if excel_importer database exists
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database='excel_importer',
            charset='utf8mb4'
        )
        
        print("✅ excel_importer database exists and is accessible!")
        connection.close()
        
    except pymysql.Error as e:
        print(f"❌ Database connection failed: {e}")
        if "Unknown database" in str(e):
            print("The 'excel_importer' database doesn't exist. Run setup_db.py first.")
        elif "Access denied" in str(e):
            print("Access denied. Check your username and password.")
        elif "Can't connect" in str(e):
            print("Can't connect to MySQL server. Make sure it's running.")
    
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    test_connection()