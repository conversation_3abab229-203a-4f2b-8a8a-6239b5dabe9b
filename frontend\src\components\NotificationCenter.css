/* Notification Center Styles */
.notification-center {
  position: relative;
}

/* Notification Trigger */
.notification-trigger {
  position: relative;
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #64748b;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.notification-trigger:hover {
  background: #f1f5f9;
  color: #334155;
}

.notification-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #ef4444;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Backdrop */
.notification-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 999;
}

/* Notification Panel */
.notification-panel {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  width: 400px;
  max-height: 600px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Panel Header */
.notification-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
}

.header-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-title h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
}

.unread-count {
  background: #3b82f6;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.mark-all-read-btn,
.clear-all-btn,
.close-btn {
  background: none;
  border: none;
  font-size: 0.875rem;
  color: #64748b;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.mark-all-read-btn:hover,
.clear-all-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Notifications List */
.notifications-list {
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
}

.empty-notifications {
  padding: 3rem 2rem;
  text-align: center;
  color: #64748b;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-notifications p {
  margin: 0 0 0.5rem 0;
  font-weight: 500;
}

.empty-notifications small {
  font-size: 0.875rem;
  opacity: 0.7;
}

/* Notification Item */
.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.notification-item:hover {
  background: rgba(59, 130, 246, 0.05);
}

.notification-item.unread {
  background: rgba(59, 130, 246, 0.02);
  border-left: 4px solid #3b82f6;
}

.notification-item.unread:hover {
  background: rgba(59, 130, 246, 0.08);
}

.notification-icon {
  font-size: 1.5rem;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-top: 0.25rem;
}

.notification-success .notification-icon {
  background: rgba(16, 185, 129, 0.1);
}

.notification-warning .notification-icon {
  background: rgba(245, 158, 11, 0.1);
}

.notification-error .notification-icon {
  background: rgba(239, 68, 68, 0.1);
}

.notification-info .notification-icon {
  background: rgba(59, 130, 246, 0.1);
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.unread-dot {
  width: 6px;
  height: 6px;
  background: #3b82f6;
  border-radius: 50%;
  flex-shrink: 0;
}

.notification-message {
  color: #64748b;
  font-size: 0.8125rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.notification-timestamp {
  color: #94a3b8;
  font-size: 0.75rem;
  font-weight: 500;
}

.delete-notification-btn {
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  font-size: 0.75rem;
  opacity: 0;
  transition: all 0.2s ease;
}

.notification-item:hover .delete-notification-btn {
  opacity: 1;
}

.delete-notification-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Panel Footer */
.notification-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
}

.view-all-btn {
  width: 100%;
  background: none;
  border: 1px solid #e2e8f0;
  color: #64748b;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  background: rgba(59, 130, 246, 0.05);
  border-color: #3b82f6;
  color: #3b82f6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-panel {
    width: 350px;
    right: -50px;
  }
}

@media (max-width: 480px) {
  .notification-panel {
    width: 300px;
    right: -100px;
  }
  
  .notification-item {
    padding: 0.75rem 1rem;
  }
  
  .notification-header {
    padding: 1rem;
  }
  
  .notification-footer {
    padding: 0.75rem 1rem;
  }
}

/* Scrollbar Styling */
.notifications-list::-webkit-scrollbar {
  width: 6px;
}

.notifications-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

.notifications-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}