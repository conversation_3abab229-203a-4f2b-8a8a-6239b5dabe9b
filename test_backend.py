"""
Test script to verify backend functionality
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"✅ Health check: {response.status_code} - {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_root():
    """Test root endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/")
        print(f"✅ Root endpoint: {response.status_code} - {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Root endpoint failed: {e}")
        return False

def test_pipeline_templates():
    """Test pipeline templates endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/api/pipeline-templates")
        if response.status_code == 200:
            templates = response.json()
            print(f"✅ Pipeline templates: Found {len(templates['templates'])} templates")
            for template in templates['templates']:
                print(f"   - {template['name']}: {len(template['fields'])} fields")
            return True
        else:
            print(f"❌ Pipeline templates failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Pipeline templates failed: {e}")
        return False

def test_file_upload():
    """Test file upload with sample data"""
    try:
        # Use one of our sample files
        with open('sample_quality_test.xlsx', 'rb') as f:
            files = {'file': ('sample_quality_test.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = requests.post(f"{BASE_URL}/upload-excel/", files=files)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ File upload: {data['rows']} rows, {len(data['columns'])} columns")
            print(f"   Columns: {', '.join(data['columns'][:5])}{'...' if len(data['columns']) > 5 else ''}")
            return True
        else:
            print(f"❌ File upload failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ File upload failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing backend functionality...")
    print()
    
    tests = [
        ("Health Check", test_health),
        ("Root Endpoint", test_root),
        ("Pipeline Templates", test_pipeline_templates),
        ("File Upload", test_file_upload)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"Testing {test_name}...")
        if test_func():
            passed += 1
        print()
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Backend is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the backend logs for details.")

if __name__ == "__main__":
    main()