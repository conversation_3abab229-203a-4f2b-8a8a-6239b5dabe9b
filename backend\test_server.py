"""
Test if the FastAPI server is responding
"""

import urllib.request
import json

def test_server():
    """Test server connectivity"""
    
    try:
        print("Testing FastAPI server...")
        
        # Test health endpoint
        with urllib.request.urlopen('http://localhost:8000/health') as response:
            data = json.loads(response.read().decode())
            print(f"✅ Health check: {data}")
        
        # Test root endpoint
        with urllib.request.urlopen('http://localhost:8000/') as response:
            data = json.loads(response.read().decode())
            print(f"✅ Root endpoint: {data}")
        
        print("\n🎉 Server is running and responding!")
        
    except Exception as e:
        print(f"❌ Server test failed: {e}")
        print("\nMake sure the server is running with:")
        print("python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000")

if __name__ == "__main__":
    test_server()