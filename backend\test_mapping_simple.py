"""
Simple test for column mapping using urllib
"""

import urllib.request
import urllib.parse
import json
import os

def test_schemas():
    """Test the schemas endpoint"""
    
    try:
        with urllib.request.urlopen('http://localhost:8000/schemas/') as response:
            data = json.loads(response.read().decode())
            print(f"✅ Found {len(data['schemas'])} schemas:")
            for schema in data['schemas']:
                print(f"   - {schema['name']}: {schema['description']}")
                print(f"     Fields: {[f['field_name'] for f in schema['fields']]}")
            return data['schemas']
    except Exception as e:
        print(f"❌ Error testing schemas: {e}")
        return []

def main():
    """Test column mapping functionality"""
    
    print("Testing column mapping functionality...\n")
    
    # Test schemas
    schemas = test_schemas()
    
    if schemas:
        print(f"\n🎉 Column mapping backend is working!")
        print(f"Available schemas: {[s['name'] for s in schemas]}")
        print("\nYou can now test the frontend:")
        print("1. Start the React frontend")
        print("2. Upload sample_employees.xlsx")
        print("3. Click 'Import with Column Mapping'")
        print("4. Select 'employees' schema")
        print("5. Review the auto-mapping suggestions")
    else:
        print("❌ Column mapping not working properly")

if __name__ == "__main__":
    main()