from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Depends, Form
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
import pandas as pd
import io
import json
import uuid
from datetime import datetime
from typing import Dict, Any, List
from database import get_db, create_tables, ImportedData, init_database
from models import TableSchema, FieldDefinition, ImportMapping, MappedData

app = FastAPI(title="Excel to MySQL Importer", version="1.0.0")

# Create database tables on startup (commented out since we created them manually)
# @app.on_event("startup")
# def startup_event():
#     create_tables()

# CORS middleware for React frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Excel to MySQL Importer API"}

@app.post("/upload-excel/")
async def upload_excel(file: UploadFile = File(...)):
    """Upload and preview Excel file"""
    
    # Validate file type
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="Invalid file type. Please upload .xlsx or .xls file")
    
    try:
        # Read Excel file
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))
        
        # Handle NaN values by replacing them with None
        df = df.where(pd.notnull(df), None)
        
        # Enhanced file analysis
        file_info = {
            "filename": file.filename,
            "file_size": len(contents),
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "columns": [],
            "preview_data": df.head(10).to_dict('records'),  # First 10 rows
            "data_summary": {
                "empty_rows": 0,
                "empty_columns": 0,
                "data_types": {},
                "potential_issues": []
            }
        }
        
        # Analyze each column in detail
        for i, col in enumerate(df.columns):
            col_data = df[col].dropna()
            
            # Detect data type and patterns
            data_type = "text"
            sample_values = col_data.head(5).tolist()
            
            if len(col_data) > 0:
                # Check if numeric
                if pd.api.types.is_numeric_dtype(df[col]):
                    data_type = "number"
                # Check if datetime
                elif pd.api.types.is_datetime64_any_dtype(df[col]):
                    data_type = "date"
                # Check patterns in text data
                elif df[col].dtype == 'object':
                    first_val = str(col_data.iloc[0]) if len(col_data) > 0 else ""
                    if '@' in first_val and '.' in first_val:
                        data_type = "email"
                    elif first_val.replace('-', '').replace('(', '').replace(')', '').replace(' ', '').isdigit():
                        data_type = "phone"
            
            column_info = {
                "index": i,
                "name": col,
                "data_type": data_type,
                "total_values": len(df),
                "non_empty_values": len(col_data),
                "empty_values": len(df) - len(col_data),
                "unique_values": int(df[col].nunique()),
                "sample_values": sample_values,
                "percentage_filled": round((len(col_data) / len(df)) * 100, 1) if len(df) > 0 else 0
            }
            
            # Add statistics for numeric columns
            if data_type == "number" and len(col_data) > 0:
                column_info.update({
                    "min_value": float(col_data.min()),
                    "max_value": float(col_data.max()),
                    "avg_value": round(float(col_data.mean()), 2)
                })
            
            file_info["columns"].append(column_info)
        
        # Check for potential issues
        empty_rows = df.isnull().all(axis=1).sum()
        empty_columns = df.isnull().all(axis=0).sum()
        
        if empty_rows > 0:
            file_info["data_summary"]["potential_issues"].append(f"{empty_rows} completely empty rows")
        if empty_columns > 0:
            file_info["data_summary"]["potential_issues"].append(f"{empty_columns} completely empty columns")
        
        # Check for inconsistent data types
        for col_info in file_info["columns"]:
            if col_info["percentage_filled"] < 50:
                file_info["data_summary"]["potential_issues"].append(f"Column '{col_info['name']}' is mostly empty ({col_info['percentage_filled']}% filled)")
        
        file_info["data_summary"]["empty_rows"] = int(empty_rows)
        file_info["data_summary"]["empty_columns"] = int(empty_columns)
        
        return file_info
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing Excel file: {str(e)}")

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.post("/test-import/")
async def test_import(file: UploadFile = File(...)):
    """Quick test endpoint to check file upload without database operations"""
    try:
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))
        
        return {
            "message": "File processed successfully (test mode)",
            "filename": file.filename,
            "rows": len(df),
            "columns": len(df.columns),
            "size_bytes": len(contents)
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing file: {str(e)}")

@app.get("/test-db")
async def test_database_connection(db: Session = Depends(get_db)):
    """Test database connection"""
    try:
        # Simple query to test connection
        from sqlalchemy import text
        result = db.execute(text("SELECT 1 as test"))
        return {"status": "database connected", "test_result": result.fetchone()}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database connection failed: {str(e)}")

@app.post("/import-data/")
async def import_data(file: UploadFile = File(...), db: Session = Depends(get_db)):
    """Import Excel data into MySQL database with optimized batch processing"""
    import time
    start_time = time.time()
    
    print(f"Starting import for file: {file.filename}")
    
    # Validate file type
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="Invalid file type")
    
    try:
        # Read Excel file
        print("Reading Excel file...")
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))
        print(f"Excel file read successfully: {len(df)} rows, {len(df.columns)} columns")
        
        # Generate unique session ID for this import
        session_id = str(uuid.uuid4())
        
        # Process in batches for better performance
        batch_size = 100  # Reduced batch size for faster response
        total_rows = len(df)
        imported_count = 0
        errors = []
        
        print(f"Processing {total_rows} rows in batches of {batch_size}")
        
        for start_idx in range(0, total_rows, batch_size):
            end_idx = min(start_idx + batch_size, total_rows)
            batch_df = df.iloc[start_idx:end_idx]
            batch_records = []
            
            for index, row in batch_df.iterrows():
                try:
                    # Convert row to JSON string
                    row_data = row.to_dict()
                    # Handle NaN values
                    for key, value in row_data.items():
                        if pd.isna(value):
                            row_data[key] = None
                    
                    # Prepare record for bulk insert
                    batch_records.append({
                        'import_session_id': session_id,
                        'row_data': json.dumps(row_data),
                        'created_at': datetime.utcnow()
                    })
                    imported_count += 1
                    
                except Exception as e:
                    errors.append(f"Row {index + 1}: {str(e)}")
            
            # Bulk insert this batch
            if batch_records:
                print(f"Inserting batch {start_idx}-{end_idx} ({len(batch_records)} records)")
                db.bulk_insert_mappings(ImportedData, batch_records)
                db.commit()
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"Import completed in {processing_time:.2f} seconds")
        
        return {
            "message": "Data imported successfully",
            "session_id": session_id,
            "imported_rows": imported_count,
            "total_rows": total_rows,
            "errors": errors,
            "processing_time": f"{processing_time:.2f} seconds"
        }
        
    except Exception as e:
        print(f"Import error: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=400, detail=f"Error importing data: {str(e)}")

@app.get("/import-history/")
async def get_import_history(db: Session = Depends(get_db)):
    """Get list of all import sessions"""
    
    try:
        # Get unique import sessions with counts
        result = db.query(
            ImportedData.import_session_id,
            ImportedData.created_at
        ).distinct().all()
        
        history = []
        for session_id, created_at in result:
            count = db.query(ImportedData).filter(
                ImportedData.import_session_id == session_id
            ).count()
            
            history.append({
                "session_id": session_id,
                "created_at": created_at.isoformat(),
                "record_count": count
            })
        
        return {"history": history}
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error fetching history: {str(e)}")

@app.get("/import-data/{session_id}")
async def get_import_data(session_id: str, db: Session = Depends(get_db)):
    """Get imported data for a specific session"""
    
    try:
        records = db.query(ImportedData).filter(
            ImportedData.import_session_id == session_id
        ).all()
        
        if not records:
            raise HTTPException(status_code=404, detail="Import session not found")
        
        data = []
        for record in records:
            row_data = json.loads(record.row_data)
            data.append({
                "id": record.id,
                "data": row_data,
                "created_at": record.created_at.isoformat()
            })
        
        return {
            "session_id": session_id,
            "records": data,
            "total_count": len(data)
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error fetching data: {str(e)}")
@app.get("/export-data/{session_id}")
async def export_data(session_id: str, db: Session = Depends(get_db)):
    """Export imported data back to Excel"""
    from fastapi.responses import StreamingResponse
    
    try:
        records = db.query(ImportedData).filter(
            ImportedData.import_session_id == session_id
        ).all()
        
        if not records:
            raise HTTPException(status_code=404, detail="Import session not found")
        
        # Convert records back to DataFrame
        data_list = []
        for record in records:
            row_data = json.loads(record.row_data)
            data_list.append(row_data)
        
        df = pd.DataFrame(data_list)
        
        # Create Excel file in memory
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Exported Data')
        
        output.seek(0)
        
        # Return as downloadable file
        return StreamingResponse(
            io.BytesIO(output.read()),
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={"Content-Disposition": f"attachment; filename=exported_data_{session_id[:8]}.xlsx"}
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error exporting data: {str(e)}")

@app.get("/stats")
async def get_stats(db: Session = Depends(get_db)):
    """Get application statistics"""
    try:
        _, SessionLocal = init_database()
        
        total_records = db.query(ImportedData).count()
        total_sessions = db.query(ImportedData.import_session_id).distinct().count()
        
        # Get latest import
        latest_import = db.query(ImportedData).order_by(ImportedData.created_at.desc()).first()
        latest_date = latest_import.created_at.isoformat() if latest_import else None
        
        return {
            "total_records": total_records,
            "total_sessions": total_sessions,
            "latest_import": latest_date
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error fetching stats: {str(e)}")
# Column Mapping Endpoints

@app.get("/schemas/")
async def get_schemas(db: Session = Depends(get_db)):
    """Get all available table schemas"""
    try:
        schemas = db.query(TableSchema).filter(TableSchema.is_active == True).all()
        result = []
        
        for schema in schemas:
            fields = []
            for field in schema.fields:
                fields.append({
                    "id": field.id,
                    "field_name": field.field_name,
                    "display_name": field.display_name,
                    "field_type": field.field_type,
                    "is_required": field.is_required,
                    "default_value": field.default_value,
                    "order_index": field.order_index
                })
            
            result.append({
                "id": schema.id,
                "name": schema.name,
                "description": schema.description,
                "fields": sorted(fields, key=lambda x: x["order_index"])
            })
        
        return {"schemas": result}
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error fetching schemas: {str(e)}")

@app.post("/schemas/")
async def create_schema(schema_data: dict, db: Session = Depends(get_db)):
    """Create a new table schema"""
    try:
        # Create schema
        new_schema = TableSchema(
            name=schema_data["name"],
            description=schema_data.get("description", "")
        )
        db.add(new_schema)
        db.flush()  # Get the ID
        
        # Create fields
        for field_data in schema_data.get("fields", []):
            new_field = FieldDefinition(
                schema_id=new_schema.id,
                field_name=field_data["field_name"],
                display_name=field_data["display_name"],
                field_type=field_data.get("field_type", "text"),
                is_required=field_data.get("is_required", False),
                default_value=field_data.get("default_value", ""),
                order_index=field_data.get("order_index", 0)
            )
            db.add(new_field)
        
        db.commit()
        
        return {
            "message": "Schema created successfully",
            "schema_id": new_schema.id
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"Error creating schema: {str(e)}")

@app.post("/preview-mapping/")
async def preview_mapping(file: UploadFile = File(...)):
    """Preview Excel columns for mapping"""
    try:
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))
        
        # Get column info
        columns_info = []
        for col in df.columns:
            sample_values = df[col].dropna().head(3).tolist()
            data_type = str(df[col].dtype)
            
            # Convert numpy types to Python types for JSON serialization
            null_count = int(df[col].isnull().sum())
            total_count = int(len(df))
            
            columns_info.append({
                "name": col,
                "data_type": data_type,
                "sample_values": sample_values,
                "null_count": null_count,
                "total_count": total_count
            })
        
        return {
            "filename": file.filename,
            "total_rows": len(df),
            "columns": columns_info
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error previewing file: {str(e)}")

@app.post("/import-with-mapping/")
async def import_with_mapping(
    file: UploadFile = File(...),
    schema_id: str = Form(None),
    mapping: str = Form(None),  # JSON string of column mappings
    db: Session = Depends(get_db)
):
    """Import data with column mapping"""
    import time
    start_time = time.time()
    
    try:
        # Parse mapping
        column_mapping = json.loads(mapping) if mapping else {}
        print(f"Received mapping: {column_mapping}")
        
        # Read Excel file
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))
        print(f"Excel file read: {len(df)} rows, {len(df.columns)} columns")
        
        # Get schema if provided
        target_schema = None
        if schema_id:
            schema_id_int = int(schema_id)
            target_schema = db.query(TableSchema).filter(TableSchema.id == schema_id_int).first()
            if not target_schema:
                raise HTTPException(status_code=404, detail="Schema not found")
            print(f"Using schema: {target_schema.name}")
        
        # Generate session ID
        session_id = str(uuid.uuid4())
        
        # Process data with mapping
        imported_count = 0
        errors = []
        batch_records = []
        
        for index, row in df.iterrows():
            try:
                mapped_data = {}
                validation_errors = []
                
                # Apply column mapping
                for excel_col, target_field in column_mapping.items():
                    if excel_col in df.columns:
                        value = row[excel_col]
                        
                        # Handle NaN values
                        if pd.isna(value):
                            value = None
                        
                        # Apply transformations if specified
                        if isinstance(target_field, dict):
                            field_name = target_field.get("field")
                            transformation = target_field.get("transform")
                            
                            if transformation == "uppercase" and value:
                                value = str(value).upper()
                            elif transformation == "lowercase" and value:
                                value = str(value).lower()
                            elif transformation == "trim" and value:
                                value = str(value).strip()
                        else:
                            field_name = target_field
                        
                        mapped_data[field_name] = value
                
                # Store mapping record (only once per session)
                if index == 0:  # Only store mapping info once
                    mapping_record = ImportMapping(
                        import_session_id=session_id,
                        excel_columns=json.dumps(list(df.columns)),
                        column_mappings=json.dumps(column_mapping),
                        transformation_rules=json.dumps({})  # For future use
                    )
                    db.add(mapping_record)
                
                # Store mapped data
                if target_schema:
                    data_record = MappedData(
                        import_session_id=session_id,
                        schema_id=target_schema.id,
                        row_data=json.dumps(mapped_data),
                        validation_errors=json.dumps(validation_errors) if validation_errors else None
                    )
                    db.add(data_record)
                else:
                    # Fallback to original ImportedData table
                    data_record = ImportedData(
                        import_session_id=session_id,
                        row_data=json.dumps(mapped_data)
                    )
                    db.add(data_record)
                
                imported_count += 1
                
            except Exception as e:
                errors.append(f"Row {index + 1}: {str(e)}")
        
        db.commit()
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        return {
            "message": "Data imported successfully with mapping",
            "session_id": session_id,
            "imported_rows": imported_count,
            "total_rows": len(df),
            "errors": errors,
            "processing_time": f"{processing_time:.2f} seconds",
            "schema_used": target_schema.name if target_schema else "default"
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"Error importing with mapping: {str(e)}")

#Dynamic Table Creation Endpoints

@app.post("/create-table-from-excel/")
async def create_table_from_excel(
    file: UploadFile = File(...),
    table_name: str = Form(...),
    table_description: str = Form(""),
    db: Session = Depends(get_db)
):
    """Create a new database table and schema from Excel structure"""
    
    try:
        # Validate table name (only alphanumeric and underscores)
        import re
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', table_name):
            raise HTTPException(status_code=400, detail="Table name must start with a letter and contain only letters, numbers, and underscores")
        
        # Check if table name already exists
        existing_schema = db.query(TableSchema).filter(TableSchema.name == table_name).first()
        if existing_schema:
            raise HTTPException(status_code=400, detail=f"Table '{table_name}' already exists")
        
        # Read Excel file to analyze structure
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))
        
        print(f"Creating table '{table_name}' from Excel with {len(df)} rows, {len(df.columns)} columns")
        
        # Handle NaN values
        df = df.where(pd.notnull(df), None)
        
        # Analyze column types
        field_definitions = []
        for i, col in enumerate(df.columns):
            # Clean column name for database field
            field_name = re.sub(r'[^a-zA-Z0-9_]', '_', col.lower().strip())
            field_name = re.sub(r'_+', '_', field_name)  # Remove multiple underscores
            field_name = field_name.strip('_')  # Remove leading/trailing underscores
            
            if not field_name or field_name[0].isdigit():
                field_name = f"field_{i+1}"
            
            # Determine field type based on data
            sample_data = df[col].dropna()
            if len(sample_data) == 0:
                field_type = "text"
            else:
                first_value = sample_data.iloc[0]
                
                # Check if it's numeric
                if pd.api.types.is_numeric_dtype(df[col]):
                    field_type = "number"
                # Check if it's datetime
                elif pd.api.types.is_datetime64_any_dtype(df[col]):
                    field_type = "date"
                # Check if it looks like an email
                elif isinstance(first_value, str) and '@' in first_value and '.' in first_value:
                    field_type = "email"
                else:
                    field_type = "text"
            
            field_definitions.append({
                "field_name": field_name,
                "display_name": col,
                "field_type": field_type,
                "is_required": False,
                "order_index": i + 1
            })
        
        # Create schema in database
        new_schema = TableSchema(
            name=table_name,
            description=table_description or f"Auto-generated from {file.filename}"
        )
        db.add(new_schema)
        db.flush()
        
        # Create field definitions
        for field_data in field_definitions:
            new_field = FieldDefinition(
                schema_id=new_schema.id,
                **field_data
            )
            db.add(new_field)
        
        # Create actual database table
        table_created = await create_physical_table(table_name, field_definitions)
        
        if table_created:
            db.commit()
            
            return {
                "message": f"Table '{table_name}' created successfully",
                "schema_id": new_schema.id,
                "table_name": table_name,
                "fields": field_definitions,
                "ready_for_import": True
            }
        else:
            db.rollback()
            raise HTTPException(status_code=500, detail="Failed to create physical database table")
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=f"Error creating table: {str(e)}")

async def create_physical_table(table_name: str, field_definitions: list):
    """Create the actual database table"""
    
    try:
        from config import DB_CONFIG
        import pymysql
        
        # Connect to database
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database'],
            charset='utf8mb4'
        )
        
        # Build CREATE TABLE SQL
        columns_sql = ["id INT AUTO_INCREMENT PRIMARY KEY"]
        
        for field in field_definitions:
            field_name = field['field_name']
            field_type = field['field_type']
            
            # Map field types to SQL types
            if field_type == "number":
                sql_type = "DECIMAL(15,2)"
            elif field_type == "date":
                sql_type = "DATE"
            elif field_type == "email":
                sql_type = "VARCHAR(255)"
            else:  # text
                sql_type = "TEXT"
            
            columns_sql.append(f"{field_name} {sql_type}")
        
        # Add metadata columns
        columns_sql.extend([
            "import_session_id VARCHAR(50)",
            "created_at DATETIME DEFAULT CURRENT_TIMESTAMP",
            "INDEX idx_session_id (import_session_id)"
        ])
        
        create_sql = f"""
        CREATE TABLE {table_name} (
            {', '.join(columns_sql)}
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        
        with connection.cursor() as cursor:
            cursor.execute(create_sql)
            print(f"✅ Physical table '{table_name}' created")
        
        connection.commit()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating physical table: {e}")
        return False

@app.post("/import-to-custom-table/")
async def import_to_custom_table(
    file: UploadFile = File(...),
    table_name: str = Form(...),
    mapping: str = Form(...),  # JSON string of column mappings
    db: Session = Depends(get_db)
):
    """Import data directly to a custom table"""
    
    try:
        # Parse mapping
        column_mapping = json.loads(mapping)
        
        # Read Excel file
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))
        
        # Get schema
        schema = db.query(TableSchema).filter(TableSchema.name == table_name).first()
        if not schema:
            raise HTTPException(status_code=404, detail=f"Table '{table_name}' not found")
        
        # Generate session ID
        session_id = str(uuid.uuid4())
        
        # Import data to custom table
        imported_count = await import_to_physical_table(
            table_name, df, column_mapping, session_id
        )
        
        # Store mapping record
        mapping_record = ImportMapping(
            import_session_id=session_id,
            excel_columns=json.dumps(list(df.columns)),
            column_mappings=json.dumps(column_mapping),
            transformation_rules=json.dumps({})
        )
        db.add(mapping_record)
        db.commit()
        
        return {
            "message": f"Data imported successfully to table '{table_name}'",
            "session_id": session_id,
            "imported_rows": imported_count,
            "total_rows": len(df),
            "table_name": table_name
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error importing to custom table: {str(e)}")

async def import_to_physical_table(table_name: str, df: pd.DataFrame, column_mapping: dict, session_id: str):
    """Import data directly to a physical database table"""
    
    try:
        from config import DB_CONFIG
        import pymysql
        
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database'],
            charset='utf8mb4'
        )
        
        imported_count = 0
        
        with connection.cursor() as cursor:
            for index, row in df.iterrows():
                # Prepare data for insertion
                field_values = {'import_session_id': session_id}
                
                for excel_col, db_field in column_mapping.items():
                    if excel_col in df.columns and db_field:
                        value = row[excel_col]
                        if pd.isna(value):
                            value = None
                        field_values[db_field] = value
                
                # Build INSERT SQL
                fields = list(field_values.keys())
                placeholders = ', '.join(['%s'] * len(fields))
                values = list(field_values.values())
                
                insert_sql = f"INSERT INTO {table_name} ({', '.join(fields)}) VALUES ({placeholders})"
                cursor.execute(insert_sql, values)
                imported_count += 1
        
        connection.commit()
        connection.close()
        
        print(f"✅ Imported {imported_count} rows to table '{table_name}'")
        return imported_count
        
    except Exception as e:
        print(f"❌ Error importing to physical table: {e}")
        raise e

@app.get("/tables/")
async def get_custom_tables(db: Session = Depends(get_db)):
    """Get list of all custom tables"""
    
    try:
        schemas = db.query(TableSchema).filter(TableSchema.is_active == True).all()
        
        tables = []
        for schema in schemas:
            # Check if physical table exists
            table_exists = await check_physical_table_exists(schema.name)
            
            tables.append({
                "id": schema.id,
                "name": schema.name,
                "description": schema.description,
                "field_count": len(schema.fields),
                "created_at": schema.created_at.isoformat(),
                "physical_table_exists": table_exists
            })
        
        return {"tables": tables}
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error fetching tables: {str(e)}")

async def check_physical_table_exists(table_name: str):
    """Check if a physical table exists in the database"""
    
    try:
        from config import DB_CONFIG
        import pymysql
        
        connection = pymysql.connect(
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            database=DB_CONFIG['database'],
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            result = cursor.fetchone()
        
        connection.close()
        return result is not None
        
    except Exception as e:
        print(f"Error checking table existence: {e}")
        return False
# Export and Reporting Endpoints

@app.get("/export-csv/{session_id}")
async def export_csv(session_id: str, db: Session = Depends(get_db)):
    """Export imported data as CSV"""
    from fastapi.responses import StreamingResponse
    import csv
    
    try:
        records = db.query(ImportedData).filter(
            ImportedData.import_session_id == session_id
        ).all()
        
        if not records:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Convert to CSV
        output = io.StringIO()
        
        if records:
            first_record = json.loads(records[0].row_data)
            fieldnames = list(first_record.keys())
            
            writer = csv.DictWriter(output, fieldnames=fieldnames)
            writer.writeheader()
            
            for record in records:
                row_data = json.loads(record.row_data)
                writer.writerow(row_data)
        
        output.seek(0)
        
        return StreamingResponse(
            io.StringIO(output.getvalue()),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename=export_{session_id[:8]}.csv"}
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Export error: {str(e)}")

@app.post("/export-quality-report/")
async def export_quality_report(report_data: dict):
    """Export data quality report as JSON"""
    from fastapi.responses import Response
    
    try:
        report_json = json.dumps(report_data, indent=2)
        
        return Response(
            content=report_json,
            media_type="application/json",
            headers={"Content-Disposition": "attachment; filename=quality_report.json"}
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Report export error: {str(e)}")

@app.post("/save-pipeline-config/")
async def save_pipeline_config(config_data: dict):
    """Save pipeline configuration"""
    from fastapi.responses import Response
    
    try:
        config_json = json.dumps(config_data, indent=2)
        
        return Response(
            content=config_json,
            media_type="application/json",
            headers={"Content-Disposition": "attachment; filename=pipeline_config.json"}
        )
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Config save error: {str(e)}")

@app.get("/preview-enhanced/{session_id}")
async def get_enhanced_preview(
    session_id: str, 
    page: int = 0, 
    size: int = 100,
    sort_by: str = None,
    filter_column: str = None,
    filter_value: str = None,
    db: Session = Depends(get_db)
):
    """Get enhanced data preview with pagination, sorting, and filtering"""
    
    try:
        query = db.query(ImportedData).filter(ImportedData.import_session_id == session_id)
        
        records = query.offset(page * size).limit(size).all()
        total_count = query.count()
        
        if not records:
            return {"data": [], "total": 0, "page": page, "size": size}
        
        # Convert to list of dictionaries
        data = []
        for record in records:
            row_data = json.loads(record.row_data)
            row_data['_id'] = record.id
            row_data['_created_at'] = record.created_at.isoformat()
            data.append(row_data)
        
        # Apply filtering if specified
        if filter_column and filter_value:
            data = [row for row in data if filter_value.lower() in str(row.get(filter_column, '')).lower()]
        
        # Apply sorting if specified
        if sort_by and sort_by in data[0] if data else False:
            reverse = sort_by.startswith('-')
            sort_key = sort_by.lstrip('-')
            data.sort(key=lambda x: str(x.get(sort_key, '')), reverse=reverse)
        
        return {
            "data": data,
            "total": total_count,
            "page": page,
            "size": size,
            "filtered": len(data) if filter_column else total_count
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Preview error: {str(e)}")

@app.get("/data-statistics/{session_id}")
async def get_data_statistics(session_id: str, db: Session = Depends(get_db)):
    """Get detailed statistics for imported data"""
    
    try:
        records = db.query(ImportedData).filter(
            ImportedData.import_session_id == session_id
        ).all()
        
        if not records:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # Analyze data
        all_data = []
        for record in records:
            row_data = json.loads(record.row_data)
            all_data.append(row_data)
        
        if not all_data:
            return {"statistics": {}}
        
        # Calculate statistics for each column
        columns = list(all_data[0].keys())
        statistics = {}
        
        for column in columns:
            values = [row.get(column) for row in all_data if row.get(column) is not None]
            
            stats = {
                "total_count": len(all_data),
                "non_null_count": len(values),
                "null_count": len(all_data) - len(values),
                "unique_count": len(set(str(v) for v in values)),
                "null_percentage": round((len(all_data) - len(values)) / len(all_data) * 100, 2)
            }
            
            # Try to calculate numeric statistics
            try:
                numeric_values = [float(v) for v in values if str(v).replace('.', '').replace('-', '').isdigit()]
                if numeric_values:
                    stats.update({
                        "min_value": min(numeric_values),
                        "max_value": max(numeric_values),
                        "avg_value": round(sum(numeric_values) / len(numeric_values), 2),
                        "numeric_count": len(numeric_values)
                    })
            except:
                pass
            
            # Most common values
            if values:
                from collections import Counter
                common_values = Counter(str(v) for v in values).most_common(5)
                stats["most_common"] = [{"value": v, "count": c} for v, c in common_values]
            
            statistics[column] = stats
        
        return {"statistics": statistics}
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Statistics error: {str(e)}")
@app.get("/api/validate/{session_id}")
async def validate_data_quality(session_id: str, db: Session = Depends(get_db)):
    """Fast data quality analysis for a session"""
    try:
        # Get a sample of records for quick analysis
        records = db.query(ImportedData).filter(
            ImportedData.import_session_id == session_id
        ).limit(100).all()  # Small sample for speed
        
        if not records:
            raise HTTPException(status_code=404, detail="No data found for session")
        
        # Get total count
        total_count = db.query(ImportedData).filter(
            ImportedData.import_session_id == session_id
        ).count()
        
        # Convert to DataFrame
        data_list = [json.loads(record.row_data) for record in records]
        df = pd.DataFrame(data_list)
        
        # Fast quality analysis
        issues = []
        missing_total = 0
        
        # Quick missing values check
        for col in df.columns:
            missing_count = df[col].isnull().sum()
            if missing_count > 0:
                missing_pct = round((missing_count / len(df)) * 100, 1)
                if missing_pct > 15:  # Only report significant missing data
                    issues.append({
                        "type": "missing_values",
                        "severity": "high" if missing_pct > 50 else "medium",
                        "column": col,
                        "description": f"{col}: {missing_pct}% missing values"
                    })
                missing_total += missing_count
        
        # Quick duplicate check
        duplicates = df.duplicated().sum()
        if duplicates > 0:
            dup_pct = round((duplicates / len(df)) * 100, 1)
            issues.append({
                "type": "duplicates",
                "severity": "medium",
                "description": f"{dup_pct}% duplicate records found"
            })
        
        # Quick format checks
        for col in df.columns:
            if 'email' in col.lower() and df[col].dtype == 'object':
                sample_data = df[col].dropna().head(5)
                invalid_emails = sum(1 for email in sample_data if '@' not in str(email))
                if invalid_emails > 1:
                    issues.append({
                        "type": "format_issues",
                        "severity": "medium",
                        "column": col,
                        "description": f"{col}: Potential email format issues"
                    })
        
        # Calculate simple quality score
        if len(issues) == 0:
            quality_score = 95
        elif len(issues) <= 2:
            quality_score = 80
        elif len(issues) <= 4:
            quality_score = 65
        else:
            quality_score = 45
        
        # Quick recommendations
        recommendations = []
        if missing_total > 0:
            recommendations.append("Review and handle missing values")
        if duplicates > 0:
            recommendations.append("Remove duplicate records")
        if any(issue["type"] == "format_issues" for issue in issues):
            recommendations.append("Validate data formats")
        if not recommendations:
            recommendations.append("Data quality looks good!")
        
        return {
            "session_id": session_id,
            "total_records": total_count,
            "analyzed_records": len(df),
            "quality_score": quality_score,
            "issues": issues,
            "recommendations": recommendations,
            "analysis_note": f"Quick analysis of {len(df)} sample records"
        }
        
        # Quick quality analysis
        quality_report = {
            "session_id": session_id,
            "total_records": total_count,
            "analyzed_records": len(df),
            "analysis_timestamp": datetime.utcnow().isoformat(),
            "issues": [],
            "summary": {
                "missing_values": 0,
                "duplicate_records": 0,
                "format_issues": 0
            },
            "column_analysis": {}
        }
        
        # Quick missing values check
        missing_data = df.isnull().sum()
        total_missing = 0
        for col, missing_count in missing_data.items():
            if missing_count > 0:
                percentage = round((missing_count / len(df)) * 100, 2)
                if percentage > 10:  # Only report if > 10% missing
                    quality_report["issues"].append({
                        "type": "missing_values",
                        "severity": "high" if percentage > 30 else "medium",
                        "column": col,
                        "percentage": percentage,
                        "description": f"Column '{col}' has {percentage}% missing values"
                    })
                total_missing += missing_count
        
        quality_report["summary"]["missing_values"] = int(total_missing)
        
        # Quick duplicate check
        duplicates = df.duplicated().sum()
        if duplicates > 0:
            dup_percentage = round((duplicates / len(df)) * 100, 2)
            quality_report["issues"].append({
                "type": "duplicate_records",
                "severity": "medium",
                "percentage": dup_percentage,
                "description": f"Found {dup_percentage}% duplicate records"
            })
            quality_report["summary"]["duplicate_records"] = int(duplicates)
        
        # Analyze each column
        for col in df.columns:
            col_data = df[col].dropna()
            
            col_analysis = {
                "data_type": str(df[col].dtype),
                "unique_values": int(df[col].nunique()),
                "missing_count": int(df[col].isnull().sum()),
                "sample_values": df[col].dropna().head(5).tolist()
            }
            
            # Check for potential data type issues
            if df[col].dtype == 'object':  # String column
                # Check for numeric-looking strings
                try:
                    numeric_pattern = df[col].dropna().astype(str).str.match(r'^-?\d+\.?\d*$')
                    if numeric_pattern.sum() > len(df) * 0.8:  # 80% look numeric
                        quality_report["issues"].append({
                            "type": "data_type_issues",
                            "severity": "low",
                            "column": col,
                            "description": f"Column '{col}' contains mostly numeric values but is stored as text",
                            "suggestion": "Consider converting to numeric type"
                        })
                        quality_report["summary"]["data_type_issues"] += 1
                except Exception:
                    pass  # Skip if regex fails
                
                # Check for email format issues
                if 'email' in col.lower():
                    try:
                        email_pattern = df[col].dropna().astype(str).str.contains(r'^[^@]+@[^@]+\.[^@]+$', regex=True)
                        invalid_emails = (~email_pattern).sum()
                        if invalid_emails > 0:
                            quality_report["issues"].append({
                                "type": "format_issues",
                                "severity": "medium",
                                "column": col,
                                "count": int(invalid_emails),
                                "description": f"Column '{col}' has {invalid_emails} invalid email formats"
                            })
                            quality_report["summary"]["format_issues"] += invalid_emails
                    except Exception:
                        pass  # Skip if regex fails
                
                # Check for phone format issues
                if 'phone' in col.lower():
                    try:
                        phone_pattern = df[col].dropna().astype(str).str.contains(r'^[\+]?[\d\-\(\)\s]+$', regex=True)
                        invalid_phones = (~phone_pattern).sum()
                        if invalid_phones > 0:
                            quality_report["issues"].append({
                                "type": "format_issues",
                                "severity": "low",
                                "column": col,
                                "count": int(invalid_phones),
                                "description": f"Column '{col}' has {invalid_phones} potentially invalid phone formats"
                            })
                            quality_report["summary"]["format_issues"] += invalid_phones
                    except Exception:
                        pass  # Skip if regex fails
            
            # Check for outliers in numeric columns
            elif df[col].dtype in ['int64', 'float64']:
                try:
                    numeric_data = df[col].dropna()
                    if len(numeric_data) > 4:  # Need at least 5 values for quartile calculation
                        Q1 = numeric_data.quantile(0.25)
                        Q3 = numeric_data.quantile(0.75)
                        IQR = Q3 - Q1
                        if IQR > 0:  # Avoid division by zero
                            outliers = df[(df[col] < (Q1 - 1.5 * IQR)) | (df[col] > (Q3 + 1.5 * IQR))][col]
                            if len(outliers) > 0:
                                quality_report["issues"].append({
                                    "type": "outliers",
                                    "severity": "low",
                                    "column": col,
                                    "count": len(outliers),
                                    "description": f"Column '{col}' has {len(outliers)} statistical outliers",
                                    "outlier_values": outliers.head(10).tolist()
                                })
                                quality_report["summary"]["outliers"] += len(outliers)
                    
                    col_analysis.update({
                        "min_value": float(df[col].min()) if pd.notna(df[col].min()) else None,
                        "max_value": float(df[col].max()) if pd.notna(df[col].max()) else None,
                        "mean_value": float(df[col].mean()) if pd.notna(df[col].mean()) else None
                    })
                except Exception:
                    pass  # Skip if numeric analysis fails
            
            quality_report["column_analysis"][col] = col_analysis
        
        # Calculate overall quality score
        total_issues = sum(quality_report["summary"].values())
        total_cells = len(df) * len(df.columns)
        quality_score = max(0, 100 - (total_issues / total_cells * 100)) if total_cells > 0 else 100
        quality_report["quality_score"] = round(quality_score, 2)
        
        # Add recommendations
        recommendations = []
        if quality_report["summary"]["missing_values"] > 0:
            recommendations.append("Consider handling missing values through imputation or removal")
        if quality_report["summary"]["duplicate_records"] > 0:
            recommendations.append("Remove or investigate duplicate records")
        if quality_report["summary"]["data_type_issues"] > 0:
            recommendations.append("Review and convert data types for better performance")
        if quality_report["summary"]["format_issues"] > 0:
            recommendations.append("Standardize data formats for consistency")
        
        quality_report["recommendations"] = recommendations
        
        return quality_report
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error validating data: {str(e)}")

@app.get("/api/enhanced-preview/{session_id}")
async def get_enhanced_preview(session_id: str, db: Session = Depends(get_db)):
    """Get enhanced preview with statistics and charts data"""
    try:
        # Get data from database
        records = db.query(ImportedData).filter(
            ImportedData.import_session_id == session_id
        ).limit(1000).all()
        
        if not records:
            raise HTTPException(status_code=404, detail="No data found for session")
        
        # Convert to DataFrame
        data_list = []
        for record in records:
            row_data = json.loads(record.row_data)
            data_list.append(row_data)
        
        df = pd.DataFrame(data_list)
        
        # Basic statistics
        preview_data = {
            "session_id": session_id,
            "total_records": len(df),
            "sample_data": data_list[:100],  # First 100 records
            "column_stats": {},
            "data_types": {},
            "charts_data": {}
        }
        
        # Analyze each column
        for col in df.columns:
            col_data = df[col].dropna()
            
            preview_data["data_types"][col] = str(df[col].dtype)
            
            if df[col].dtype in ['int64', 'float64']:
                # Numeric column statistics
                preview_data["column_stats"][col] = {
                    "type": "numeric",
                    "count": int(len(col_data)),
                    "missing": int(df[col].isnull().sum()),
                    "unique": int(df[col].nunique()),
                    "min": float(col_data.min()) if len(col_data) > 0 else None,
                    "max": float(col_data.max()) if len(col_data) > 0 else None,
                    "mean": float(col_data.mean()) if len(col_data) > 0 else None,
                    "median": float(col_data.median()) if len(col_data) > 0 else None,
                    "std": float(col_data.std()) if len(col_data) > 0 else None
                }
                
                # Create histogram data
                if len(col_data) > 0:
                    import numpy as np
                    hist, bins = np.histogram(col_data, bins=10)
                    preview_data["charts_data"][col] = {
                        "type": "histogram",
                        "data": hist.tolist(),
                        "labels": [f"{bins[i]:.2f}-{bins[i+1]:.2f}" for i in range(len(bins)-1)]
                    }
            
            else:
                # Categorical column statistics
                value_counts = df[col].value_counts().head(10)
                preview_data["column_stats"][col] = {
                    "type": "categorical",
                    "count": int(len(col_data)),
                    "missing": int(df[col].isnull().sum()),
                    "unique": int(df[col].nunique()),
                    "top_values": value_counts.to_dict()
                }
                
                # Create bar chart data for top values
                if len(value_counts) > 0:
                    preview_data["charts_data"][col] = {
                        "type": "bar",
                        "data": value_counts.values.tolist(),
                        "labels": value_counts.index.tolist()
                    }
        
        return preview_data
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error getting enhanced preview: {str(e)}")

@app.get("/api/pipeline-templates")
async def get_pipeline_templates():
    """Get available pipeline templates"""
    templates = [
        {
            "id": "employee_data",
            "name": "Employee Data Import",
            "description": "Standard template for importing employee information",
            "fields": [
                {"name": "employee_id", "type": "text", "required": True},
                {"name": "first_name", "type": "text", "required": True},
                {"name": "last_name", "type": "text", "required": True},
                {"name": "email", "type": "email", "required": True},
                {"name": "department", "type": "text", "required": False},
                {"name": "hire_date", "type": "date", "required": False},
                {"name": "salary", "type": "number", "required": False}
            ]
        },
        {
            "id": "customer_data",
            "name": "Customer Data Import",
            "description": "Template for importing customer information",
            "fields": [
                {"name": "customer_id", "type": "text", "required": True},
                {"name": "company_name", "type": "text", "required": True},
                {"name": "contact_name", "type": "text", "required": False},
                {"name": "email", "type": "email", "required": False},
                {"name": "phone", "type": "text", "required": False},
                {"name": "address", "type": "text", "required": False},
                {"name": "city", "type": "text", "required": False},
                {"name": "country", "type": "text", "required": False}
            ]
        },
        {
            "id": "financial_data",
            "name": "Financial Transactions",
            "description": "Template for importing financial transaction data",
            "fields": [
                {"name": "transaction_id", "type": "text", "required": True},
                {"name": "account_number", "type": "text", "required": True},
                {"name": "transaction_date", "type": "date", "required": True},
                {"name": "amount", "type": "number", "required": True},
                {"name": "currency", "type": "text", "required": False},
                {"name": "description", "type": "text", "required": False},
                {"name": "category", "type": "text", "required": False}
            ]
        },
        {
            "id": "product_catalog",
            "name": "Product Catalog",
            "description": "Template for importing product information",
            "fields": [
                {"name": "product_id", "type": "text", "required": True},
                {"name": "product_name", "type": "text", "required": True},
                {"name": "category", "type": "text", "required": False},
                {"name": "price", "type": "number", "required": False},
                {"name": "quantity", "type": "number", "required": False},
                {"name": "description", "type": "text", "required": False},
                {"name": "supplier", "type": "text", "required": False}
            ]
        }
    ]
    
    return {"templates": templates}