"""
Test the import endpoint directly
"""

import pandas as pd
import io
import urllib.request
import urllib.parse
import json

def test_import_endpoint():
    """Test the import endpoint with a small test file"""
    
    print("Creating test Excel file...")
    
    # Create test data
    test_data = {
        'Name': ['Alice Test', 'Bob Test'],
        'Age': [25, 30],
        'Email': ['<EMAIL>', '<EMAIL>'],
        'Department': ['IT', 'HR']
    }
    
    df = pd.DataFrame(test_data)
    
    # Create Excel file in memory
    excel_buffer = io.BytesIO()
    df.to_excel(excel_buffer, index=False, engine='openpyxl')
    excel_data = excel_buffer.getvalue()
    
    print(f"✅ Created test Excel file with {len(df)} rows ({len(excel_data)} bytes)")
    
    # Test the quick test endpoint first
    print("\nTesting quick test endpoint...")
    try:
        # Prepare multipart form data
        boundary = '----WebKitFormBoundary7MA4YWxkTrZu0gW'
        
        body = (
            f'--{boundary}\r\n'
            f'Content-Disposition: form-data; name="file"; filename="test.xlsx"\r\n'
            f'Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\r\n'
            f'\r\n'
        ).encode('utf-8')
        
        body += excel_data
        body += f'\r\n--{boundary}--\r\n'.encode('utf-8')
        
        # Create request
        req = urllib.request.Request(
            'http://localhost:8000/test-import/',
            data=body,
            headers={
                'Content-Type': f'multipart/form-data; boundary={boundary}',
                'Content-Length': str(len(body))
            },
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=30) as response:
            result = json.loads(response.read().decode())
            print(f"✅ Test endpoint response: {result}")
        
        # Now test the actual import endpoint
        print("\nTesting actual import endpoint...")
        
        req = urllib.request.Request(
            'http://localhost:8000/import-data/',
            data=body,
            headers={
                'Content-Type': f'multipart/form-data; boundary={boundary}',
                'Content-Length': str(len(body))
            },
            method='POST'
        )
        
        with urllib.request.urlopen(req, timeout=30) as response:
            result = json.loads(response.read().decode())
            print(f"✅ Import endpoint response: {result}")
            
        print("\n🎉 Both endpoints working successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print(f"Error type: {type(e)}")

if __name__ == "__main__":
    test_import_endpoint()