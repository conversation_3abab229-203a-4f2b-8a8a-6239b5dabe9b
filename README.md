# Excel to MySQL Importer

A web application for uploading Excel files and importing data into MySQL database.

## Tech Stack
- **Backend:** FastAPI + pandas + SQLAlchemy + MySQL
- **Frontend:** React + Axios
- **Database:** MySQL

## Project Structure
```
excel-importer/
├── backend/          # FastAPI application
├── frontend/         # React application
└── README.md
```

## Getting Started

### Prerequisites
- Python 3.8+
- Node.js 14+
- MySQL Server

### Database Setup
1. Install and start MySQL server
2. Edit `backend/config.py` and set your MySQL credentials:
```python
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'your_mysql_password',  # Set your password here
    'database': 'excel_importer'
}
```
3. Create database and tables:
```bash
cd backend
python simple_setup.py
```

### Backend Setup
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload
```

### Frontend Setup
```bash
cd frontend
npm install
npm start
```

### Quick Start Scripts
- `start_backend.bat` - Start FastAPI server
- `start_frontend.bat` - Start React development server

### Sample Files
The project includes sample Excel files for testing:
- `sample_employees.xlsx` - Standard employee data (5 records)
- `sample_customers.xlsx` - Customer data (5 records)  
- `sample_mixed_format.xlsx` - Different naming conventions (5 records)
- `sample_large_dataset.xlsx` - Performance testing (100 records)
- `sample_problematic_data.xlsx` - Data validation testing (5 records)

See `SAMPLE_FILES_README.md` for detailed descriptions.

## Features
- [x] File upload with validation
- [x] Excel file preview (first 5 rows)
- [x] Data import to MySQL database
- [x] Import session tracking
- [x] Error handling and reporting
- [x] Import history view
- [x] Data export functionality
- [x] Application statistics
- [x] **Column mapping interface** - Map Excel columns to database fields
- [x] **Schema management** - Pre-defined schemas (employees, customers)
- [x] **Auto-mapping suggestions** - Smart column name matching
- [x] **Data validation** - Required field validation
- [x] **Dynamic table creation** - Create new database tables from Excel structure
- [x] **Custom table import** - Import directly to user-created tables
- [x] **Automatic field type detection** - Smart detection of text, number, email, date fields
- [ ] Advanced data validation rules
- [ ] Table management UI (edit, delete tables)
- [ ] Batch processing for very large files