"""
Test import performance with optimized settings
"""

import pandas as pd
import time
from database import init_database, ImportedData
import json
import uuid
from datetime import datetime

def test_bulk_import():
    """Test bulk import performance"""
    
    # Create test data
    test_data = []
    for i in range(1000):  # 1000 test records
        test_data.append({
            'Name': f'Test User {i}',
            'Age': 20 + (i % 50),
            'Email': f'user{i}@test.com',
            'Department': ['Engineering', 'Marketing', 'Sales', 'HR'][i % 4],
            'Salary': 50000 + (i * 100)
        })
    
    df = pd.DataFrame(test_data)
    print(f"Created test DataFrame with {len(df)} rows")
    
    # Initialize database
    engine, SessionLocal = init_database()
    db = SessionLocal()
    
    try:
        session_id = str(uuid.uuid4())
        
        # Test bulk insert performance
        start_time = time.time()
        
        batch_records = []
        for index, row in df.iterrows():
            row_data = row.to_dict()
            # Handle NaN values
            for key, value in row_data.items():
                if pd.isna(value):
                    row_data[key] = None
            
            batch_records.append({
                'import_session_id': session_id,
                'row_data': json.dumps(row_data),
                'created_at': datetime.utcnow()
            })
        
        # Bulk insert
        db.bulk_insert_mappings(ImportedData, batch_records)
        db.commit()
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"✅ Imported {len(batch_records)} records in {processing_time:.2f} seconds")
        print(f"   Performance: {len(batch_records)/processing_time:.0f} records/second")
        
        # Verify data was inserted
        count = db.query(ImportedData).filter(ImportedData.import_session_id == session_id).count()
        print(f"✅ Verified {count} records in database")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    test_bulk_import()