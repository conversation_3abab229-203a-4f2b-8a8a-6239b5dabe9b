"""
Test the column mapping functionality
"""

import requests
import json

def test_column_mapping():
    """Test the column mapping endpoints"""
    
    base_url = "http://localhost:8000"
    
    print("Testing column mapping functionality...\n")
    
    # Test 1: Get available schemas
    print("1. Testing schemas endpoint...")
    try:
        response = requests.get(f"{base_url}/schemas/")
        if response.status_code == 200:
            schemas = response.json()
            print(f"✅ Found {len(schemas['schemas'])} schemas")
            for schema in schemas['schemas']:
                print(f"   - {schema['name']}: {len(schema['fields'])} fields")
        else:
            print(f"❌ Schemas endpoint failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Error testing schemas: {e}")
        return
    
    # Test 2: Preview mapping with sample file
    print("\n2. Testing preview mapping...")
    try:
        with open('../sample_employees.xlsx', 'rb') as f:
            files = {'file': ('sample_employees.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = requests.post(f"{base_url}/preview-mapping/", files=files)
            
        if response.status_code == 200:
            preview = response.json()
            print(f"✅ Preview successful: {preview['total_rows']} rows, {len(preview['columns'])} columns")
            for col in preview['columns']:
                print(f"   - {col['name']}: {col['data_type']} (samples: {col['sample_values']})")
        else:
            print(f"❌ Preview mapping failed: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ Error testing preview: {e}")
    
    # Test 3: Test import with mapping
    print("\n3. Testing import with mapping...")
    try:
        # Simple mapping for employees schema
        test_mapping = {
            "Full Name": "name",
            "Email Address": "email",
            "Years": "age",
            "Dept": "department"
        }
        
        with open('../sample_employees.xlsx', 'rb') as f:
            files = {'file': ('sample_employees.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            data = {
                'schema_id': '1',  # Assuming employees schema has ID 1
                'mapping': json.dumps(test_mapping)
            }
            response = requests.post(f"{base_url}/import-with-mapping/", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Import successful: {result['imported_rows']} rows imported")
            print(f"   Session ID: {result['session_id']}")
            print(f"   Processing time: {result['processing_time']}")
        else:
            print(f"❌ Import with mapping failed: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ Error testing import: {e}")
    
    print("\n🎉 Column mapping tests completed!")

if __name__ == "__main__":
    test_column_mapping()